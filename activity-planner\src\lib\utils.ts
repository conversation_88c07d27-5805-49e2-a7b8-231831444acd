import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function getWeatherIcon(condition: string): string {
  const weatherIcons: Record<string, string> = {
    'clear': '☀️',
    'sunny': '☀️',
    'cloudy': '☁️',
    'partly-cloudy': '⛅',
    'overcast': '☁️',
    'rain': '🌧️',
    'drizzle': '🌦️',
    'snow': '❄️',
    'thunderstorm': '⛈️',
    'fog': '🌫️',
    'windy': '💨',
  }
  
  return weatherIcons[condition.toLowerCase()] || '🌤️'
}

export function getCategoryIcon(category: string): string {
  const categoryIcons: Record<string, string> = {
    'date': '💕',
    'outdoor': '🌲',
    'indoor': '🏠',
    'adventure': '🎢',
    'cultural': '🎭',
    'food': '🍽️',
    'sports': '⚽',
    'relaxation': '🧘',
    'entertainment': '🎬',
    'travel': '✈️',
    'learning': '📚',
    'social': '👥',
  }
  
  return categoryIcons[category.toLowerCase()] || '📝'
}

export function getActivityStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    'planned': 'bg-blue-100 text-blue-800',
    'completed': 'bg-green-100 text-green-800',
    'cancelled': 'bg-red-100 text-red-800',
    'in-progress': 'bg-yellow-100 text-yellow-800',
  }
  
  return statusColors[status] || 'bg-gray-100 text-gray-800'
}
