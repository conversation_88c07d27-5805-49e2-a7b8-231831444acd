var u=Object.defineProperty;var r=t=>u(t,"__esModule",{value:!0});var y=(t,o)=>{r(t);for(var s in o)u(t,s,{get:o[s],enumerable:!0})};y(exports,{default:()=>b,stableHash:()=>c});const i=new WeakMap;let p=0;function c(t){const o=typeof t,s=t&&t.constructor,f=s==Date;if(Object(t)===t&&!f&&s!=RegExp){let e=i.get(t);if(e)return e;e=++p+"~",i.set(t,e);let n;if(s==Array){for(e="@",n=0;n<t.length;n++)e+=c(t[n])+",";i.set(t,e)}else if(s==Object){e="#";const l=Object.keys(t).sort();for(;(n=l.pop())!==void 0;)t[n]!==void 0&&(e+=n+":"+c(t[n])+",");i.set(t,e)}return e}return f?t.toJSON():o=="symbol"?t.toString():o=="string"?JSON.stringify(t):""+t}var b=c;
