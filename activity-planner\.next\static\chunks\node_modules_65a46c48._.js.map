{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/typeof.js"], "sourcesContent": ["function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nexport { _typeof as default };"], "names": [], "mappings": ";;;AAAA,SAAS,QAAQ,CAAC;IAChB;IAEA,OAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,QAAQ,GAAG,SAAU,CAAC;QAC9F,OAAO,OAAO;IAChB,IAAI,SAAU,CAAC;QACb,OAAO,KAAK,cAAc,OAAO,UAAU,EAAE,WAAW,KAAK,UAAU,MAAM,OAAO,SAAS,GAAG,WAAW,OAAO;IACpH,GAAG,QAAQ;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/toPrimitive.js"], "sourcesContent": ["import _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,YAAY,CAAC,EAAE,CAAC;IACvB,IAAI,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,MAAM,CAAC,GAAG,OAAO;IACzC,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAC7B,IAAI,KAAK,MAAM,GAAG;QAChB,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QACvB,IAAI,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,IAAI,OAAO;QACnC,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/toPropertyKey.js"], "sourcesContent": ["import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nexport { toPropertyKey as default };"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,SAAS,cAAc,CAAC;IACtB,IAAI,IAAI,CAAA,GAAA,sKAAA,CAAA,UAAW,AAAD,EAAE,GAAG;IACvB,OAAO,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,KAAK,IAAI,IAAI;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/defineProperty.js"], "sourcesContent": ["import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nexport { _defineProperty as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAC9B,OAAO,CAAC,IAAI,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAC/D,OAAO;QACP,YAAY,CAAC;QACb,cAAc,CAAC;QACf,UAAU,CAAC;IACb,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/objectSpread2.js"], "sourcesContent": ["import defineProperty from \"./defineProperty.js\";\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nexport { _objectSpread2 as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,CAAC,EAAE,CAAC;IACnB,IAAI,IAAI,OAAO,IAAI,CAAC;IACpB,IAAI,OAAO,qBAAqB,EAAE;QAChC,IAAI,IAAI,OAAO,qBAAqB,CAAC;QACrC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAC5B,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QACzD,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IACvB;IACA,OAAO;AACT;AACA,SAAS,eAAe,CAAC;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAC/C,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAChD,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;QAC3B,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAC9I,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QACjE;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js"], "sourcesContent": ["function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };"], "names": [], "mappings": ";;;AAAA,SAAS,8BAA8B,CAAC,EAAE,CAAC;IACzC,IAAI,QAAQ,GAAG,OAAO,CAAC;IACvB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QACjD,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QACzB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACb;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/objectWithoutProperties.js"], "sourcesContent": ["import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,yBAAyB,CAAC,EAAE,CAAC;IACpC,IAAI,QAAQ,GAAG,OAAO,CAAC;IACvB,IAAI,GACF,GACA,IAAI,CAAA,GAAA,uLAAA,CAAA,UAA4B,AAAD,EAAE,GAAG;IACtC,IAAI,OAAO,qBAAqB,EAAE;QAChC,IAAI,IAAI,OAAO,qBAAqB,CAAC;QACrC,IAAK,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAA,CAAC,CAAA,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACpH;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/classCallCheck.js"], "sourcesContent": ["function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,IAAI,CAAC,CAAC,aAAa,CAAC,GAAG,MAAM,IAAI,UAAU;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/createClass.js"], "sourcesContent": ["import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nexport { _createClass as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,kBAAkB,CAAC,EAAE,CAAC;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;QACjC,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,EAAE,UAAU,GAAG,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,YAAY,GAAG,CAAC,GAAG,WAAW,KAAK,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,cAAc,CAAC,GAAG,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,EAAE,GAAG,GAAG;IAC5I;AACF;AACA,SAAS,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3B,OAAO,KAAK,kBAAkB,EAAE,SAAS,EAAE,IAAI,KAAK,kBAAkB,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,aAAa;QACjH,UAAU,CAAC;IACb,IAAI;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/getPrototypeOf.js"], "sourcesContent": ["function _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\nexport { _getPrototypeOf as default };"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,CAAC;IACxB,OAAO,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAU,CAAC;QACzF,OAAO,EAAE,SAAS,IAAI,OAAO,cAAc,CAAC;IAC9C,GAAG,gBAAgB;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/isNativeReflectConstruct.js"], "sourcesContent": ["function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,IAAI;QACF,IAAI,IAAI,CAAC,QAAQ,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,SAAS,EAAE,EAAE,YAAa;IACtF,EAAE,OAAO,GAAG,CAAC;IACb,OAAO,CAAC,4BAA4B,SAAS;QAC3C,OAAO,CAAC,CAAC;IACX,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/assertThisInitialized.js"], "sourcesContent": ["function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };"], "names": [], "mappings": ";;;AAAA,SAAS,uBAAuB,CAAC;IAC/B,IAAI,KAAK,MAAM,GAAG,MAAM,IAAI,eAAe;IAC3C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/possibleConstructorReturn.js"], "sourcesContent": ["import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nexport { _possibleConstructorReturn as default };"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,SAAS,2BAA2B,CAAC,EAAE,CAAC;IACtC,IAAI,KAAK,CAAC,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,MAAM,cAAc,OAAO,CAAC,GAAG,OAAO;IACpE,IAAI,KAAK,MAAM,GAAG,MAAM,IAAI,UAAU;IACtC,OAAO,CAAA,GAAA,gLAAA,CAAA,UAAqB,AAAD,EAAE;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/callSuper.js"], "sourcesContent": ["import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _callSuper(t, o, e) {\n  return o = getPrototypeOf(o), possibleConstructorReturn(t, isNativeReflectConstruct() ? Reflect.construct(o, e || [], getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nexport { _callSuper as default };"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;IACzB,OAAO,IAAI,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,IAAI,CAAA,GAAA,oLAAA,CAAA,UAAyB,AAAD,EAAE,GAAG,CAAA,GAAA,mLAAA,CAAA,UAAwB,AAAD,MAAM,QAAQ,SAAS,CAAC,GAAG,KAAK,EAAE,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,GAAG,WAAW,IAAI,EAAE,KAAK,CAAC,GAAG;AACpK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/setPrototypeOf.js"], "sourcesContent": ["function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,OAAO,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;QAC5F,OAAO,EAAE,SAAS,GAAG,GAAG;IAC1B,GAAG,gBAAgB,GAAG;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/inherits.js"], "sourcesContent": ["import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nexport { _inherits as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,CAAC,EAAE,CAAC;IACrB,IAAI,cAAc,OAAO,KAAK,SAAS,GAAG,MAAM,IAAI,UAAU;IAC9D,EAAE,SAAS,GAAG,OAAO,MAAM,CAAC,KAAK,EAAE,SAAS,EAAE;QAC5C,aAAa;YACX,OAAO;YACP,UAAU,CAAC;YACX,cAAc,CAAC;QACjB;IACF,IAAI,OAAO,cAAc,CAAC,GAAG,aAAa;QACxC,UAAU,CAAC;IACb,IAAI,KAAK,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,GAAG;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/arrayWithHoles.js"], "sourcesContent": ["function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,CAAC;IACxB,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/iterableToArrayLimit.js"], "sourcesContent": ["function _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nexport { _iterableToArrayLimit as default };"], "names": [], "mappings": ";;;AAAA,SAAS,sBAAsB,CAAC,EAAE,CAAC;IACjC,IAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa;IAChG,IAAI,QAAQ,GAAG;QACb,IAAI,GACF,GACA,GACA,GACA,IAAI,EAAE,EACN,IAAI,CAAC,GACL,IAAI,CAAC;QACP,IAAI;YACF,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,GAAG;gBACrC,IAAI,OAAO,OAAO,GAAG;gBACrB,IAAI,CAAC;YACP,OAAO,MAAO,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK,GAAG,EAAE,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;QACvF,EAAE,OAAO,GAAG;YACV,IAAI,CAAC,GAAG,IAAI;QACd,SAAU;YACR,IAAI;gBACF,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG;YACzE,SAAU;gBACR,IAAI,GAAG,MAAM;YACf;QACF;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/arrayLikeToArray.js"], "sourcesContent": ["function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nexport { _arrayLikeToArray as default };"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB,CAAC,EAAE,CAAC;IAC7B,CAAC,QAAQ,KAAK,IAAI,EAAE,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACrD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/unsupportedIterableToArray.js"], "sourcesContent": ["import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nexport { _unsupportedIterableToArray as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,4BAA4B,CAAC,EAAE,CAAC;IACvC,IAAI,GAAG;QACL,IAAI,YAAY,OAAO,GAAG,OAAO,CAAA,GAAA,2KAAA,CAAA,UAAgB,AAAD,EAAE,GAAG;QACrD,IAAI,IAAI,CAAA,CAAC,CAAA,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;QACtC,OAAO,aAAa,KAAK,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,GAAG,UAAU,KAAK,UAAU,IAAI,MAAM,IAAI,CAAC,KAAK,gBAAgB,KAAK,2CAA2C,IAAI,CAAC,KAAK,CAAA,GAAA,2KAAA,CAAA,UAAgB,AAAD,EAAE,GAAG,KAAK,KAAK;IAC3N;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/nonIterableRest.js"], "sourcesContent": ["function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableRest as default };"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,MAAM,IAAI,UAAU;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/slicedToArray.js"], "sourcesContent": ["import arrayWithHoles from \"./arrayWithHoles.js\";\nimport iterableToArrayLimit from \"./iterableToArrayLimit.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableRest from \"./nonIterableRest.js\";\nfunction _slicedToArray(r, e) {\n  return arrayWithHoles(r) || iterableToArrayLimit(r, e) || unsupportedIterableToArray(r, e) || nonIterableRest();\n}\nexport { _slicedToArray as default };"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,eAAe,CAAC,EAAE,CAAC;IAC1B,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,MAAM,CAAA,GAAA,+KAAA,CAAA,UAAoB,AAAD,EAAE,GAAG,MAAM,CAAA,GAAA,qLAAA,CAAA,UAA0B,AAAD,EAAE,GAAG,MAAM,CAAA,GAAA,0KAAA,CAAA,UAAe,AAAD;AAC9G", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/extends.js"], "sourcesContent": ["function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,2CAMjD,SAAS,KAAK,CAAC,MAAM;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/inheritsLoose.js"], "sourcesContent": ["import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,eAAe,CAAC,EAAE,CAAC;IAC1B,EAAE,SAAS,GAAG,OAAO,MAAM,CAAC,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC,WAAW,GAAG,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,GAAG;AAC3F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/arrayWithoutHoles.js"], "sourcesContent": ["import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return arrayLikeToArray(r);\n}\nexport { _arrayWithoutHoles as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,mBAAmB,CAAC;IAC3B,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO,CAAA,GAAA,2KAAA,CAAA,UAAgB,AAAD,EAAE;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/iterableToArray.js"], "sourcesContent": ["function _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nexport { _iterableToArray as default };"], "names": [], "mappings": ";;;AAAA,SAAS,iBAAiB,CAAC;IACzB,IAAI,eAAe,OAAO,UAAU,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,aAAa,EAAE,OAAO,MAAM,IAAI,CAAC;AAC/G", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/nonIterableSpread.js"], "sourcesContent": ["function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableSpread as default };"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,MAAM,IAAI,UAAU;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/toConsumableArray.js"], "sourcesContent": ["import arrayWithoutHoles from \"./arrayWithoutHoles.js\";\nimport iterableToArray from \"./iterableToArray.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableSpread from \"./nonIterableSpread.js\";\nfunction _toConsumableArray(r) {\n  return arrayWithoutHoles(r) || iterableToArray(r) || unsupportedIterableToArray(r) || nonIterableSpread();\n}\nexport { _toConsumableArray as default };"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,mBAAmB,CAAC;IAC3B,OAAO,CAAA,GAAA,4KAAA,CAAA,UAAiB,AAAD,EAAE,MAAM,CAAA,GAAA,0KAAA,CAAA,UAAe,AAAD,EAAE,MAAM,CAAA,GAAA,qLAAA,CAAA,UAA0B,AAAD,EAAE,MAAM,CAAA,GAAA,4KAAA,CAAA,UAAiB,AAAD;AACxG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40babel/runtime/helpers/esm/toArray.js"], "sourcesContent": ["import arrayWithHoles from \"./arrayWithHoles.js\";\nimport iterableToArray from \"./iterableToArray.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableRest from \"./nonIterableRest.js\";\nfunction _toArray(r) {\n  return arrayWithHoles(r) || iterableToArray(r) || unsupportedIterableToArray(r) || nonIterableRest();\n}\nexport { _toArray as default };"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,SAAS,CAAC;IACjB,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,MAAM,CAAA,GAAA,0KAAA,CAAA,UAAe,AAAD,EAAE,MAAM,CAAA,GAAA,qLAAA,CAAA,UAA0B,AAAD,EAAE,MAAM,CAAA,GAAA,0KAAA,CAAA,UAAe,AAAD;AACnG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-big-calendar/node_modules/clsx/dist/clsx.m.js"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f);else for(t in e)e[t]&&(n&&(n+=\" \"),n+=t);return n}export function clsx(){for(var e,t,f=0,n=\"\";f<arguments.length;)(e=arguments[f++])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;SAAO,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAE,CAAC,IAAE,SAAS,CAAC,IAAI,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/invariant/browser.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar invariant = function(condition, format, a, b, c, d, e, f) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error(\n        'Minified exception occurred; use the non-minified dev environment ' +\n        'for the full error message and additional helpful warnings.'\n      );\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(\n        format.replace(/%s/g, function() { return args[argIndex++]; })\n      );\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n};\n\nmodule.exports = invariant;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAgBK;AAdN;AAEA;;;;;;;;;CASC,GAED,IAAI,YAAY,SAAS,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC1D,wCAA2C;QACzC,IAAI,WAAW,WAAW;YACxB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,IAAI,CAAC,WAAW;QACd,IAAI;QACJ,IAAI,WAAW,WAAW;YACxB,QAAQ,IAAI,MACV,uEACA;QAEJ,OAAO;YACL,IAAI,OAAO;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;aAAE;YAC7B,IAAI,WAAW;YACf,QAAQ,IAAI,MACV,OAAO,OAAO,CAAC,OAAO;gBAAa,OAAO,IAAI,CAAC,WAAW;YAAE;YAE9D,MAAM,IAAI,GAAG;QACf;QAEA,MAAM,WAAW,GAAG,GAAG,4CAA4C;QACnE,MAAM;IACR;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/uncontrollable/lib/esm/utils.js"], "sourcesContent": ["import invariant from 'invariant';\n\nvar noop = function noop() {};\n\nfunction readOnlyPropType(handler, name) {\n  return function (props, propName) {\n    if (props[propName] !== undefined) {\n      if (!props[handler]) {\n        return new Error(\"You have provided a `\" + propName + \"` prop to `\" + name + \"` \" + (\"without an `\" + handler + \"` handler prop. This will render a read-only field. \") + (\"If the field should be mutable use `\" + defaultKey(propName) + \"`. \") + (\"Otherwise, set `\" + handler + \"`.\"));\n      }\n    }\n  };\n}\n\nexport function uncontrolledPropTypes(controlledValues, displayName) {\n  var propTypes = {};\n  Object.keys(controlledValues).forEach(function (prop) {\n    // add default propTypes for folks that use runtime checks\n    propTypes[defaultKey(prop)] = noop;\n\n    if (process.env.NODE_ENV !== 'production') {\n      var handler = controlledValues[prop];\n      !(typeof handler === 'string' && handler.trim().length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Uncontrollable - [%s]: the prop `%s` needs a valid handler key name in order to make it uncontrollable', displayName, prop) : invariant(false) : void 0;\n      propTypes[prop] = readOnlyPropType(handler, displayName);\n    }\n  });\n  return propTypes;\n}\nexport function isProp(props, prop) {\n  return props[prop] !== undefined;\n}\nexport function defaultKey(key) {\n  return 'default' + key.charAt(0).toUpperCase() + key.substr(1);\n}\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\nexport function canAcceptRef(component) {\n  return !!component && (typeof component !== 'function' || component.prototype && component.prototype.isReactComponent);\n}"], "names": [], "mappings": ";;;;;;AAoBQ;AApBR;;AAEA,IAAI,OAAO,SAAS,QAAQ;AAE5B,SAAS,iBAAiB,OAAO,EAAE,IAAI;IACrC,OAAO,SAAU,KAAK,EAAE,QAAQ;QAC9B,IAAI,KAAK,CAAC,SAAS,KAAK,WAAW;YACjC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACnB,OAAO,IAAI,MAAM,0BAA0B,WAAW,gBAAgB,OAAO,OAAO,CAAC,iBAAiB,UAAU,sDAAsD,IAAI,CAAC,yCAAyC,WAAW,YAAY,KAAK,IAAI,CAAC,qBAAqB,UAAU,IAAI;YAC1R;QACF;IACF;AACF;AAEO,SAAS,sBAAsB,gBAAgB,EAAE,WAAW;IACjE,IAAI,YAAY,CAAC;IACjB,OAAO,IAAI,CAAC,kBAAkB,OAAO,CAAC,SAAU,IAAI;QAClD,0DAA0D;QAC1D,SAAS,CAAC,WAAW,MAAM,GAAG;QAE9B,wCAA2C;YACzC,IAAI,UAAU,gBAAgB,CAAC,KAAK;YACpC,CAAC,CAAC,OAAO,YAAY,YAAY,QAAQ,IAAI,GAAG,MAAM,IAAI,uCAAwC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE,OAAO,0GAA0G,aAAa,+CAA2B,KAAK;YAC1Q,SAAS,CAAC,KAAK,GAAG,iBAAiB,SAAS;QAC9C;IACF;IACA,OAAO;AACT;AACO,SAAS,OAAO,KAAK,EAAE,IAAI;IAChC,OAAO,KAAK,CAAC,KAAK,KAAK;AACzB;AACO,SAAS,WAAW,GAAG;IAC5B,OAAO,YAAY,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,MAAM,CAAC;AAC9D;AAUO,SAAS,aAAa,SAAS;IACpC,OAAO,CAAC,CAAC,aAAa,CAAC,OAAO,cAAc,cAAc,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,gBAAgB;AACvH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/uncontrollable/lib/esm/hook.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\n\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\n\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n\nimport { useCallback, useRef, useState } from 'react';\nimport * as Utils from './utils';\n\nfunction useUncontrolledProp(propValue, defaultValue, handler) {\n  var wasPropRef = useRef(propValue !== undefined);\n\n  var _useState = useState(defaultValue),\n      stateValue = _useState[0],\n      setState = _useState[1];\n\n  var isProp = propValue !== undefined;\n  var wasProp = wasPropRef.current;\n  wasPropRef.current = isProp;\n  /**\n   * If a prop switches from controlled to Uncontrolled\n   * reset its value to the defaultValue\n   */\n\n  if (!isProp && wasProp && stateValue !== defaultValue) {\n    setState(defaultValue);\n  }\n\n  return [isProp ? propValue : stateValue, useCallback(function (value) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    if (handler) handler.apply(void 0, [value].concat(args));\n    setState(value);\n  }, [handler])];\n}\n\nexport { useUncontrolledProp };\nexport default function useUncontrolled(props, config) {\n  return Object.keys(config).reduce(function (result, fieldName) {\n    var _extends2;\n\n    var _ref = result,\n        defaultValue = _ref[Utils.defaultKey(fieldName)],\n        propsValue = _ref[fieldName],\n        rest = _objectWithoutPropertiesLoose(_ref, [Utils.defaultKey(fieldName), fieldName].map(_toPropertyKey));\n\n    var handlerName = config[fieldName];\n\n    var _useUncontrolledProp = useUncontrolledProp(propsValue, defaultValue, props[handlerName]),\n        value = _useUncontrolledProp[0],\n        handler = _useUncontrolledProp[1];\n\n    return _extends({}, rest, (_extends2 = {}, _extends2[fieldName] = value, _extends2[handlerName] = handler, _extends2));\n  }, props);\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAMA;AACA;;;AALA,SAAS,eAAe,GAAG;IAAI,IAAI,MAAM,aAAa,KAAK;IAAW,OAAO,OAAO,QAAQ,WAAW,MAAM,OAAO;AAAM;AAE1H,SAAS,aAAa,KAAK,EAAE,IAAI;IAAI,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM,OAAO;IAAO,IAAI,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,SAAS,WAAW;QAAE,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,QAAQ;QAAY,IAAI,OAAO,QAAQ,UAAU,OAAO;QAAK,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,SAAS,WAAW,SAAS,MAAM,EAAE;AAAQ;;;AAKxX,SAAS,oBAAoB,SAAS,EAAE,YAAY,EAAE,OAAO;IAC3D,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,cAAc;IAEtC,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,eACrB,aAAa,SAAS,CAAC,EAAE,EACzB,WAAW,SAAS,CAAC,EAAE;IAE3B,IAAI,SAAS,cAAc;IAC3B,IAAI,UAAU,WAAW,OAAO;IAChC,WAAW,OAAO,GAAG;IACrB;;;GAGC,GAED,IAAI,CAAC,UAAU,WAAW,eAAe,cAAc;QACrD,SAAS;IACX;IAEA,OAAO;QAAC,SAAS,YAAY;QAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,SAAU,KAAK;gBAClE,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,MAAM,OAAQ;oBAC1G,IAAI,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,KAAK;gBAClC;gBAEA,IAAI,SAAS,QAAQ,KAAK,CAAC,KAAK,GAAG;oBAAC;iBAAM,CAAC,MAAM,CAAC;gBAClD,SAAS;YACX;8CAAG;YAAC;SAAQ;KAAE;AAChB;;AAGe,SAAS,gBAAgB,KAAK,EAAE,MAAM;IACnD,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,MAAM,EAAE,SAAS;QAC3D,IAAI;QAEJ,IAAI,OAAO,QACP,eAAe,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,aAAgB,AAAD,EAAE,WAAW,EAChD,aAAa,IAAI,CAAC,UAAU,EAC5B,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,MAAM;YAAC,CAAA,GAAA,wJAAA,CAAA,aAAgB,AAAD,EAAE;YAAY;SAAU,CAAC,GAAG,CAAC;QAE5F,IAAI,cAAc,MAAM,CAAC,UAAU;QAEnC,IAAI,uBAAuB,oBAAoB,YAAY,cAAc,KAAK,CAAC,YAAY,GACvF,QAAQ,oBAAoB,CAAC,EAAE,EAC/B,UAAU,oBAAoB,CAAC,EAAE;QAErC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,UAAU,GAAG,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,SAAS;IACtH,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/uncontrollable/lib/esm/uncontrollable.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nvar _jsxFileName = \"/Users/<USER>/src/uncontrollable/src/uncontrollable.js\";\nimport React from 'react';\nimport { polyfill } from 'react-lifecycles-compat';\nimport invariant from 'invariant';\nimport * as Utils from './utils';\nexport default function uncontrollable(Component, controlledValues, methods) {\n  if (methods === void 0) {\n    methods = [];\n  }\n\n  var displayName = Component.displayName || Component.name || 'Component';\n  var canAcceptRef = Utils.canAcceptRef(Component);\n  var controlledProps = Object.keys(controlledValues);\n  var PROPS_TO_OMIT = controlledProps.map(Utils.defaultKey);\n  !(canAcceptRef || !methods.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, '[uncontrollable] stateless function components cannot pass through methods ' + 'because they have no associated instances. Check component: ' + displayName + ', ' + 'attempting to pass through methods: ' + methods.join(', ')) : invariant(false) : void 0;\n\n  var UncontrolledComponent =\n  /*#__PURE__*/\n  function (_React$Component) {\n    _inheritsLoose(UncontrolledComponent, _React$Component);\n\n    function UncontrolledComponent() {\n      var _this;\n\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n      _this.handlers = Object.create(null);\n      controlledProps.forEach(function (propName) {\n        var handlerName = controlledValues[propName];\n\n        var handleChange = function handleChange(value) {\n          if (_this.props[handlerName]) {\n            var _this$props;\n\n            _this._notifying = true;\n\n            for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n              args[_key2 - 1] = arguments[_key2];\n            }\n\n            (_this$props = _this.props)[handlerName].apply(_this$props, [value].concat(args));\n\n            _this._notifying = false;\n          }\n\n          if (!_this.unmounted) _this.setState(function (_ref) {\n            var _extends2;\n\n            var values = _ref.values;\n            return {\n              values: _extends(Object.create(null), values, (_extends2 = {}, _extends2[propName] = value, _extends2))\n            };\n          });\n        };\n\n        _this.handlers[handlerName] = handleChange;\n      });\n      if (methods.length) _this.attachRef = function (ref) {\n        _this.inner = ref;\n      };\n      var values = Object.create(null);\n      controlledProps.forEach(function (key) {\n        values[key] = _this.props[Utils.defaultKey(key)];\n      });\n      _this.state = {\n        values: values,\n        prevProps: {}\n      };\n      return _this;\n    }\n\n    var _proto = UncontrolledComponent.prototype;\n\n    _proto.shouldComponentUpdate = function shouldComponentUpdate() {\n      //let setState trigger the update\n      return !this._notifying;\n    };\n\n    UncontrolledComponent.getDerivedStateFromProps = function getDerivedStateFromProps(props, _ref2) {\n      var values = _ref2.values,\n          prevProps = _ref2.prevProps;\n      var nextState = {\n        values: _extends(Object.create(null), values),\n        prevProps: {}\n      };\n      controlledProps.forEach(function (key) {\n        /**\n         * If a prop switches from controlled to Uncontrolled\n         * reset its value to the defaultValue\n         */\n        nextState.prevProps[key] = props[key];\n\n        if (!Utils.isProp(props, key) && Utils.isProp(prevProps, key)) {\n          nextState.values[key] = props[Utils.defaultKey(key)];\n        }\n      });\n      return nextState;\n    };\n\n    _proto.componentWillUnmount = function componentWillUnmount() {\n      this.unmounted = true;\n    };\n\n    _proto.render = function render() {\n      var _this2 = this;\n\n      var _this$props2 = this.props,\n          innerRef = _this$props2.innerRef,\n          props = _objectWithoutPropertiesLoose(_this$props2, [\"innerRef\"]);\n\n      PROPS_TO_OMIT.forEach(function (prop) {\n        delete props[prop];\n      });\n      var newProps = {};\n      controlledProps.forEach(function (propName) {\n        var propValue = _this2.props[propName];\n        newProps[propName] = propValue !== undefined ? propValue : _this2.state.values[propName];\n      });\n      return React.createElement(Component, _extends({}, props, newProps, this.handlers, {\n        ref: innerRef || this.attachRef\n      }));\n    };\n\n    return UncontrolledComponent;\n  }(React.Component);\n\n  polyfill(UncontrolledComponent);\n  UncontrolledComponent.displayName = \"Uncontrolled(\" + displayName + \")\";\n  UncontrolledComponent.propTypes = _extends({\n    innerRef: function innerRef() {}\n  }, Utils.uncontrolledPropTypes(controlledValues, displayName));\n  methods.forEach(function (method) {\n    UncontrolledComponent.prototype[method] = function $proxiedMethod() {\n      var _this$inner;\n\n      return (_this$inner = this.inner)[method].apply(_this$inner, arguments);\n    };\n  });\n  var WrappedComponent = UncontrolledComponent;\n\n  if (React.forwardRef) {\n    WrappedComponent = React.forwardRef(function (props, ref) {\n      return React.createElement(UncontrolledComponent, _extends({}, props, {\n        innerRef: ref,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 128\n        },\n        __self: this\n      }));\n    });\n    WrappedComponent.propTypes = UncontrolledComponent.propTypes;\n  }\n\n  WrappedComponent.ControlledComponent = Component;\n  /**\n   * useful when wrapping a Component and you want to control\n   * everything\n   */\n\n  WrappedComponent.deferControlTo = function (newComponent, additions, nextMethods) {\n    if (additions === void 0) {\n      additions = {};\n    }\n\n    return uncontrollable(newComponent, _extends({}, controlledValues, additions), nextMethods);\n  };\n\n  return WrappedComponent;\n}"], "names": [], "mappings": ";;;AAiBuC;AAjBvC;AACA;AACA;AAEA;AACA;AACA;AACA;;;;AAJA,IAAI,eAAe;;;;;AAKJ,SAAS,eAAe,SAAS,EAAE,gBAAgB,EAAE,OAAO;IACzE,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,EAAE;IACd;IAEA,IAAI,cAAc,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI;IAC7D,IAAI,eAAe,CAAA,GAAA,wJAAA,CAAA,eAAkB,AAAD,EAAE;IACtC,IAAI,kBAAkB,OAAO,IAAI,CAAC;IAClC,IAAI,gBAAgB,gBAAgB,GAAG,CAAC,wJAAA,CAAA,aAAgB;IACxD,CAAC,CAAC,gBAAgB,CAAC,QAAQ,MAAM,IAAI,uCAAwC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE,OAAO,gFAAgF,iEAAiE,cAAc,OAAO,yCAAyC,QAAQ,IAAI,CAAC,gDAA4B,KAAK;IAE3V,IAAI,wBACJ,WAAW,GACX,SAAU,gBAAgB;QACxB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,uBAAuB;QAEtC,SAAS;YACP,IAAI;YAEJ,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;gBACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;YAC9B;YAEA,QAAQ,iBAAiB,IAAI,CAAC,KAAK,CAAC,kBAAkB;gBAAC,IAAI;aAAC,CAAC,MAAM,CAAC,UAAU,IAAI;YAClF,MAAM,QAAQ,GAAG,OAAO,MAAM,CAAC;YAC/B,gBAAgB,OAAO,CAAC,SAAU,QAAQ;gBACxC,IAAI,cAAc,gBAAgB,CAAC,SAAS;gBAE5C,IAAI,eAAe,SAAS,aAAa,KAAK;oBAC5C,IAAI,MAAM,KAAK,CAAC,YAAY,EAAE;wBAC5B,IAAI;wBAEJ,MAAM,UAAU,GAAG;wBAEnB,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI,QAAQ,GAAG,QAAQ,OAAO,QAAS;4BACjH,IAAI,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC,MAAM;wBACpC;wBAEA,CAAC,cAAc,MAAM,KAAK,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,aAAa;4BAAC;yBAAM,CAAC,MAAM,CAAC;wBAE3E,MAAM,UAAU,GAAG;oBACrB;oBAEA,IAAI,CAAC,MAAM,SAAS,EAAE,MAAM,QAAQ,CAAC,SAAU,IAAI;wBACjD,IAAI;wBAEJ,IAAI,SAAS,KAAK,MAAM;wBACxB,OAAO;4BACL,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,OAAO,MAAM,CAAC,OAAO,QAAQ,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,SAAS,GAAG,OAAO,SAAS;wBACvG;oBACF;gBACF;gBAEA,MAAM,QAAQ,CAAC,YAAY,GAAG;YAChC;YACA,IAAI,QAAQ,MAAM,EAAE,MAAM,SAAS,GAAG,SAAU,GAAG;gBACjD,MAAM,KAAK,GAAG;YAChB;YACA,IAAI,SAAS,OAAO,MAAM,CAAC;YAC3B,gBAAgB,OAAO,CAAC,SAAU,GAAG;gBACnC,MAAM,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,CAAA,GAAA,wJAAA,CAAA,aAAgB,AAAD,EAAE,KAAK;YAClD;YACA,MAAM,KAAK,GAAG;gBACZ,QAAQ;gBACR,WAAW,CAAC;YACd;YACA,OAAO;QACT;QAEA,IAAI,SAAS,sBAAsB,SAAS;QAE5C,OAAO,qBAAqB,GAAG,SAAS;YACtC,iCAAiC;YACjC,OAAO,CAAC,IAAI,CAAC,UAAU;QACzB;QAEA,sBAAsB,wBAAwB,GAAG,SAAS,yBAAyB,KAAK,EAAE,KAAK;YAC7F,IAAI,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS;YAC/B,IAAI,YAAY;gBACd,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,OAAO,MAAM,CAAC,OAAO;gBACtC,WAAW,CAAC;YACd;YACA,gBAAgB,OAAO,CAAC,SAAU,GAAG;gBACnC;;;SAGC,GACD,UAAU,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;gBAErC,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,SAAY,AAAD,EAAE,OAAO,QAAQ,CAAA,GAAA,wJAAA,CAAA,SAAY,AAAD,EAAE,WAAW,MAAM;oBAC7D,UAAU,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA,GAAA,wJAAA,CAAA,aAAgB,AAAD,EAAE,KAAK;gBACtD;YACF;YACA,OAAO;QACT;QAEA,OAAO,oBAAoB,GAAG,SAAS;YACrC,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,OAAO,MAAM,GAAG,SAAS;YACvB,IAAI,SAAS,IAAI;YAEjB,IAAI,eAAe,IAAI,CAAC,KAAK,EACzB,WAAW,aAAa,QAAQ,EAChC,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,cAAc;gBAAC;aAAW;YAEpE,cAAc,OAAO,CAAC,SAAU,IAAI;gBAClC,OAAO,KAAK,CAAC,KAAK;YACpB;YACA,IAAI,WAAW,CAAC;YAChB,gBAAgB,OAAO,CAAC,SAAU,QAAQ;gBACxC,IAAI,YAAY,OAAO,KAAK,CAAC,SAAS;gBACtC,QAAQ,CAAC,SAAS,GAAG,cAAc,YAAY,YAAY,OAAO,KAAK,CAAC,MAAM,CAAC,SAAS;YAC1F;YACA,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO,UAAU,IAAI,CAAC,QAAQ,EAAE;gBACjF,KAAK,YAAY,IAAI,CAAC,SAAS;YACjC;QACF;QAEA,OAAO;IACT,EAAE,6JAAA,CAAA,UAAK,CAAC,SAAS;IAEjB,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD,EAAE;IACT,sBAAsB,WAAW,GAAG,kBAAkB,cAAc;IACpE,sBAAsB,SAAS,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACzC,UAAU,SAAS,YAAY;IACjC,GAAG,CAAA,GAAA,wJAAA,CAAA,wBAA2B,AAAD,EAAE,kBAAkB;IACjD,QAAQ,OAAO,CAAC,SAAU,MAAM;QAC9B,sBAAsB,SAAS,CAAC,OAAO,GAAG,SAAS;YACjD,IAAI;YAEJ,OAAO,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa;QAC/D;IACF;IACA,IAAI,mBAAmB;IAEvB,IAAI,6JAAA,CAAA,UAAK,CAAC,UAAU,EAAE;QACpB,mBAAmB,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC,SAAU,KAAK,EAAE,GAAG;YACtD,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,uBAAuB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;gBACpE,UAAU;gBACV,UAAU;oBACR,UAAU;oBACV,YAAY;gBACd;gBACA,QAAQ,IAAI;YACd;QACF;QACA,iBAAiB,SAAS,GAAG,sBAAsB,SAAS;IAC9D;IAEA,iBAAiB,mBAAmB,GAAG;IACvC;;;GAGC,GAED,iBAAiB,cAAc,GAAG,SAAU,YAAY,EAAE,SAAS,EAAE,WAAW;QAC9E,IAAI,cAAc,KAAK,GAAG;YACxB,YAAY,CAAC;QACf;QAEA,OAAO,eAAe,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,kBAAkB,YAAY;IACjF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 898, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/uncontrollable/lib/esm/index.js"], "sourcesContent": ["export { default as useUncontrolled, useUncontrolledProp } from './hook';\nexport { default as uncontrollable } from './uncontrollable';"], "names": [], "mappings": ";AAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-lifecycles-compat/react-lifecycles-compat.es.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nfunction componentWillMount() {\n  // Call this.constructor.gDSFP to support sub-classes.\n  var state = this.constructor.getDerivedStateFromProps(this.props, this.state);\n  if (state !== null && state !== undefined) {\n    this.setState(state);\n  }\n}\n\nfunction componentWillReceiveProps(nextProps) {\n  // Call this.constructor.gDSFP to support sub-classes.\n  // Use the setState() updater to ensure state isn't stale in certain edge cases.\n  function updater(prevState) {\n    var state = this.constructor.getDerivedStateFromProps(nextProps, prevState);\n    return state !== null && state !== undefined ? state : null;\n  }\n  // Binding \"this\" is important for shallow renderer support.\n  this.setState(updater.bind(this));\n}\n\nfunction componentWillUpdate(nextProps, nextState) {\n  try {\n    var prevProps = this.props;\n    var prevState = this.state;\n    this.props = nextProps;\n    this.state = nextState;\n    this.__reactInternalSnapshotFlag = true;\n    this.__reactInternalSnapshot = this.getSnapshotBeforeUpdate(\n      prevProps,\n      prevState\n    );\n  } finally {\n    this.props = prevProps;\n    this.state = prevState;\n  }\n}\n\n// React may warn about cWM/cWRP/cWU methods being deprecated.\n// Add a flag to suppress these warnings for this special case.\ncomponentWillMount.__suppressDeprecationWarning = true;\ncomponentWillReceiveProps.__suppressDeprecationWarning = true;\ncomponentWillUpdate.__suppressDeprecationWarning = true;\n\nfunction polyfill(Component) {\n  var prototype = Component.prototype;\n\n  if (!prototype || !prototype.isReactComponent) {\n    throw new Error('Can only polyfill class components');\n  }\n\n  if (\n    typeof Component.getDerivedStateFromProps !== 'function' &&\n    typeof prototype.getSnapshotBeforeUpdate !== 'function'\n  ) {\n    return Component;\n  }\n\n  // If new component APIs are defined, \"unsafe\" lifecycles won't be called.\n  // Error if any of these lifecycles are present,\n  // Because they would work differently between older and newer (16.3+) versions of React.\n  var foundWillMountName = null;\n  var foundWillReceivePropsName = null;\n  var foundWillUpdateName = null;\n  if (typeof prototype.componentWillMount === 'function') {\n    foundWillMountName = 'componentWillMount';\n  } else if (typeof prototype.UNSAFE_componentWillMount === 'function') {\n    foundWillMountName = 'UNSAFE_componentWillMount';\n  }\n  if (typeof prototype.componentWillReceiveProps === 'function') {\n    foundWillReceivePropsName = 'componentWillReceiveProps';\n  } else if (typeof prototype.UNSAFE_componentWillReceiveProps === 'function') {\n    foundWillReceivePropsName = 'UNSAFE_componentWillReceiveProps';\n  }\n  if (typeof prototype.componentWillUpdate === 'function') {\n    foundWillUpdateName = 'componentWillUpdate';\n  } else if (typeof prototype.UNSAFE_componentWillUpdate === 'function') {\n    foundWillUpdateName = 'UNSAFE_componentWillUpdate';\n  }\n  if (\n    foundWillMountName !== null ||\n    foundWillReceivePropsName !== null ||\n    foundWillUpdateName !== null\n  ) {\n    var componentName = Component.displayName || Component.name;\n    var newApiName =\n      typeof Component.getDerivedStateFromProps === 'function'\n        ? 'getDerivedStateFromProps()'\n        : 'getSnapshotBeforeUpdate()';\n\n    throw Error(\n      'Unsafe legacy lifecycles will not be called for components using new component APIs.\\n\\n' +\n        componentName +\n        ' uses ' +\n        newApiName +\n        ' but also contains the following legacy lifecycles:' +\n        (foundWillMountName !== null ? '\\n  ' + foundWillMountName : '') +\n        (foundWillReceivePropsName !== null\n          ? '\\n  ' + foundWillReceivePropsName\n          : '') +\n        (foundWillUpdateName !== null ? '\\n  ' + foundWillUpdateName : '') +\n        '\\n\\nThe above lifecycles should be removed. Learn more about this warning here:\\n' +\n        'https://fb.me/react-async-component-lifecycle-hooks'\n    );\n  }\n\n  // React <= 16.2 does not support static getDerivedStateFromProps.\n  // As a workaround, use cWM and cWRP to invoke the new static lifecycle.\n  // Newer versions of React will ignore these lifecycles if gDSFP exists.\n  if (typeof Component.getDerivedStateFromProps === 'function') {\n    prototype.componentWillMount = componentWillMount;\n    prototype.componentWillReceiveProps = componentWillReceiveProps;\n  }\n\n  // React <= 16.2 does not support getSnapshotBeforeUpdate.\n  // As a workaround, use cWU to invoke the new lifecycle.\n  // Newer versions of React will ignore that lifecycle if gSBU exists.\n  if (typeof prototype.getSnapshotBeforeUpdate === 'function') {\n    if (typeof prototype.componentDidUpdate !== 'function') {\n      throw new Error(\n        'Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype'\n      );\n    }\n\n    prototype.componentWillUpdate = componentWillUpdate;\n\n    var componentDidUpdate = prototype.componentDidUpdate;\n\n    prototype.componentDidUpdate = function componentDidUpdatePolyfill(\n      prevProps,\n      prevState,\n      maybeSnapshot\n    ) {\n      // 16.3+ will not execute our will-update method;\n      // It will pass a snapshot value to did-update though.\n      // Older versions will require our polyfilled will-update value.\n      // We need to handle both cases, but can't just check for the presence of \"maybeSnapshot\",\n      // Because for <= 15.x versions this might be a \"prevContext\" object.\n      // We also can't just check \"__reactInternalSnapshot\",\n      // Because get-snapshot might return a falsy value.\n      // So check for the explicit __reactInternalSnapshotFlag flag to determine behavior.\n      var snapshot = this.__reactInternalSnapshotFlag\n        ? this.__reactInternalSnapshot\n        : maybeSnapshot;\n\n      componentDidUpdate.call(this, prevProps, prevState, snapshot);\n    };\n  }\n\n  return Component;\n}\n\nexport { polyfill };\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED,SAAS;IACP,sDAAsD;IACtD,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK;IAC5E,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,IAAI,CAAC,QAAQ,CAAC;IAChB;AACF;AAEA,SAAS,0BAA0B,SAAS;IAC1C,sDAAsD;IACtD,gFAAgF;IAChF,SAAS,QAAQ,SAAS;QACxB,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,WAAW;QACjE,OAAO,UAAU,QAAQ,UAAU,YAAY,QAAQ;IACzD;IACA,4DAA4D;IAC5D,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,IAAI;AACjC;AAEA,SAAS,oBAAoB,SAAS,EAAE,SAAS;IAC/C,IAAI;QACF,IAAI,YAAY,IAAI,CAAC,KAAK;QAC1B,IAAI,YAAY,IAAI,CAAC,KAAK;QAC1B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,2BAA2B,GAAG;QACnC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CACzD,WACA;IAEJ,SAAU;QACR,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;IACf;AACF;AAEA,8DAA8D;AAC9D,+DAA+D;AAC/D,mBAAmB,4BAA4B,GAAG;AAClD,0BAA0B,4BAA4B,GAAG;AACzD,oBAAoB,4BAA4B,GAAG;AAEnD,SAAS,SAAS,SAAS;IACzB,IAAI,YAAY,UAAU,SAAS;IAEnC,IAAI,CAAC,aAAa,CAAC,UAAU,gBAAgB,EAAE;QAC7C,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,OAAO,UAAU,wBAAwB,KAAK,cAC9C,OAAO,UAAU,uBAAuB,KAAK,YAC7C;QACA,OAAO;IACT;IAEA,0EAA0E;IAC1E,gDAAgD;IAChD,yFAAyF;IACzF,IAAI,qBAAqB;IACzB,IAAI,4BAA4B;IAChC,IAAI,sBAAsB;IAC1B,IAAI,OAAO,UAAU,kBAAkB,KAAK,YAAY;QACtD,qBAAqB;IACvB,OAAO,IAAI,OAAO,UAAU,yBAAyB,KAAK,YAAY;QACpE,qBAAqB;IACvB;IACA,IAAI,OAAO,UAAU,yBAAyB,KAAK,YAAY;QAC7D,4BAA4B;IAC9B,OAAO,IAAI,OAAO,UAAU,gCAAgC,KAAK,YAAY;QAC3E,4BAA4B;IAC9B;IACA,IAAI,OAAO,UAAU,mBAAmB,KAAK,YAAY;QACvD,sBAAsB;IACxB,OAAO,IAAI,OAAO,UAAU,0BAA0B,KAAK,YAAY;QACrE,sBAAsB;IACxB;IACA,IACE,uBAAuB,QACvB,8BAA8B,QAC9B,wBAAwB,MACxB;QACA,IAAI,gBAAgB,UAAU,WAAW,IAAI,UAAU,IAAI;QAC3D,IAAI,aACF,OAAO,UAAU,wBAAwB,KAAK,aAC1C,+BACA;QAEN,MAAM,MACJ,6FACE,gBACA,WACA,aACA,wDACA,CAAC,uBAAuB,OAAO,SAAS,qBAAqB,EAAE,IAC/D,CAAC,8BAA8B,OAC3B,SAAS,4BACT,EAAE,IACN,CAAC,wBAAwB,OAAO,SAAS,sBAAsB,EAAE,IACjE,sFACA;IAEN;IAEA,kEAAkE;IAClE,wEAAwE;IACxE,wEAAwE;IACxE,IAAI,OAAO,UAAU,wBAAwB,KAAK,YAAY;QAC5D,UAAU,kBAAkB,GAAG;QAC/B,UAAU,yBAAyB,GAAG;IACxC;IAEA,0DAA0D;IAC1D,wDAAwD;IACxD,qEAAqE;IACrE,IAAI,OAAO,UAAU,uBAAuB,KAAK,YAAY;QAC3D,IAAI,OAAO,UAAU,kBAAkB,KAAK,YAAY;YACtD,MAAM,IAAI,MACR;QAEJ;QAEA,UAAU,mBAAmB,GAAG;QAEhC,IAAI,qBAAqB,UAAU,kBAAkB;QAErD,UAAU,kBAAkB,GAAG,SAAS,2BACtC,SAAS,EACT,SAAS,EACT,aAAa;YAEb,iDAAiD;YACjD,sDAAsD;YACtD,gEAAgE;YAChE,0FAA0F;YAC1F,qEAAqE;YACrE,sDAAsD;YACtD,mDAAmD;YACnD,oFAAoF;YACpF,IAAI,WAAW,IAAI,CAAC,2BAA2B,GAC3C,IAAI,CAAC,uBAAuB,GAC5B;YAEJ,mBAAmB,IAAI,CAAC,IAAI,EAAE,WAAW,WAAW;QACtD;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1043, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-is/cjs/react-is.development.js"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAMG;AAJJ;AAIA,wCAA2C;IACzC,CAAC;QACH;QAEA,mFAAmF;QACnF,6DAA6D;QAC7D,IAAI,YAAY,OAAO,WAAW,cAAc,OAAO,GAAG;QAC1D,IAAI,qBAAqB,YAAY,OAAO,GAAG,CAAC,mBAAmB;QACnE,IAAI,oBAAoB,YAAY,OAAO,GAAG,CAAC,kBAAkB;QACjE,IAAI,sBAAsB,YAAY,OAAO,GAAG,CAAC,oBAAoB;QACrE,IAAI,yBAAyB,YAAY,OAAO,GAAG,CAAC,uBAAuB;QAC3E,IAAI,sBAAsB,YAAY,OAAO,GAAG,CAAC,oBAAoB;QACrE,IAAI,sBAAsB,YAAY,OAAO,GAAG,CAAC,oBAAoB;QACrE,IAAI,qBAAqB,YAAY,OAAO,GAAG,CAAC,mBAAmB,QAAQ,8EAA8E;QACzJ,qEAAqE;QAErE,IAAI,wBAAwB,YAAY,OAAO,GAAG,CAAC,sBAAsB;QACzE,IAAI,6BAA6B,YAAY,OAAO,GAAG,CAAC,2BAA2B;QACnF,IAAI,yBAAyB,YAAY,OAAO,GAAG,CAAC,uBAAuB;QAC3E,IAAI,sBAAsB,YAAY,OAAO,GAAG,CAAC,oBAAoB;QACrE,IAAI,2BAA2B,YAAY,OAAO,GAAG,CAAC,yBAAyB;QAC/E,IAAI,kBAAkB,YAAY,OAAO,GAAG,CAAC,gBAAgB;QAC7D,IAAI,kBAAkB,YAAY,OAAO,GAAG,CAAC,gBAAgB;QAC7D,IAAI,mBAAmB,YAAY,OAAO,GAAG,CAAC,iBAAiB;QAC/D,IAAI,yBAAyB,YAAY,OAAO,GAAG,CAAC,uBAAuB;QAC3E,IAAI,uBAAuB,YAAY,OAAO,GAAG,CAAC,qBAAqB;QACvE,IAAI,mBAAmB,YAAY,OAAO,GAAG,CAAC,iBAAiB;QAE/D,SAAS,mBAAmB,IAAI;YAC9B,OAAO,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,gFAAgF;YACjJ,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,QAAQ,CAAC,KAAK,QAAQ,KAAK,mBAAmB,KAAK,QAAQ,KAAK,mBAAmB,KAAK,QAAQ,KAAK,uBAAuB,KAAK,QAAQ,KAAK,sBAAsB,KAAK,QAAQ,KAAK,0BAA0B,KAAK,QAAQ,KAAK,0BAA0B,KAAK,QAAQ,KAAK,wBAAwB,KAAK,QAAQ,KAAK,oBAAoB,KAAK,QAAQ,KAAK,gBAAgB;QACpmB;QAEA,SAAS,OAAO,MAAM;YACpB,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;gBACjD,IAAI,WAAW,OAAO,QAAQ;gBAE9B,OAAQ;oBACN,KAAK;wBACH,IAAI,OAAO,OAAO,IAAI;wBAEtB,OAAQ;4BACN,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH,OAAO;4BAET;gCACE,IAAI,eAAe,QAAQ,KAAK,QAAQ;gCAExC,OAAQ;oCACN,KAAK;oCACL,KAAK;oCACL,KAAK;oCACL,KAAK;oCACL,KAAK;wCACH,OAAO;oCAET;wCACE,OAAO;gCACX;wBAEJ;oBAEF,KAAK;wBACH,OAAO;gBACX;YACF;YAEA,OAAO;QACT,EAAE,iDAAiD;QAEnD,IAAI,YAAY;QAChB,IAAI,iBAAiB;QACrB,IAAI,kBAAkB;QACtB,IAAI,kBAAkB;QACtB,IAAI,UAAU;QACd,IAAI,aAAa;QACjB,IAAI,WAAW;QACf,IAAI,OAAO;QACX,IAAI,OAAO;QACX,IAAI,SAAS;QACb,IAAI,WAAW;QACf,IAAI,aAAa;QACjB,IAAI,WAAW;QACf,IAAI,sCAAsC,OAAO,iCAAiC;QAElF,SAAS,YAAY,MAAM;YACzB;gBACE,IAAI,CAAC,qCAAqC;oBACxC,sCAAsC,MAAM,kDAAkD;oBAE9F,OAAO,CAAC,OAAO,CAAC,0DAA0D,+DAA+D;gBAC3I;YACF;YAEA,OAAO,iBAAiB,WAAW,OAAO,YAAY;QACxD;QACA,SAAS,iBAAiB,MAAM;YAC9B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,kBAAkB,MAAM;YAC/B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,kBAAkB,MAAM;YAC/B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,UAAU,MAAM;YACvB,OAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,QAAQ,KAAK;QAC9E;QACA,SAAS,aAAa,MAAM;YAC1B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,WAAW,MAAM;YACxB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,OAAO,MAAM;YACpB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,OAAO,MAAM;YACpB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,SAAS,MAAM;YACtB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,WAAW,MAAM;YACxB,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,aAAa,MAAM;YAC1B,OAAO,OAAO,YAAY;QAC5B;QACA,SAAS,WAAW,MAAM;YACxB,OAAO,OAAO,YAAY;QAC5B;QAEA,QAAQ,SAAS,GAAG;QACpB,QAAQ,cAAc,GAAG;QACzB,QAAQ,eAAe,GAAG;QAC1B,QAAQ,eAAe,GAAG;QAC1B,QAAQ,OAAO,GAAG;QAClB,QAAQ,UAAU,GAAG;QACrB,QAAQ,QAAQ,GAAG;QACnB,QAAQ,IAAI,GAAG;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,QAAQ,GAAG;QACnB,QAAQ,UAAU,GAAG;QACrB,QAAQ,QAAQ,GAAG;QACnB,QAAQ,WAAW,GAAG;QACtB,QAAQ,gBAAgB,GAAG;QAC3B,QAAQ,iBAAiB,GAAG;QAC5B,QAAQ,iBAAiB,GAAG;QAC5B,QAAQ,SAAS,GAAG;QACpB,QAAQ,YAAY,GAAG;QACvB,QAAQ,UAAU,GAAG;QACrB,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;QACjB,QAAQ,QAAQ,GAAG;QACnB,QAAQ,UAAU,GAAG;QACrB,QAAQ,YAAY,GAAG;QACvB,QAAQ,UAAU,GAAG;QACrB,QAAQ,kBAAkB,GAAG;QAC7B,QAAQ,MAAM,GAAG;IACf,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-is/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/next/src/build/polyfills/object-assign.ts"], "sourcesContent": ["var assign = Object.assign.bind(Object)\nmodule.exports = assign\nmodule.exports.default = module.exports\n"], "names": ["assign", "Object", "bind", "module", "exports", "default"], "mappings": ";AAAA,IAAIA,SAASC,OAAOD,MAAM,CAACE,IAAI,CAACD;AAChCE,OAAOC,OAAO,GAAGJ;AACjBG,OAAOC,OAAO,CAACC,OAAO,GAAGF,OAAOC,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/prop-types/lib/ReactPropTypesSecret.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;AAEA,IAAI,uBAAuB;AAE3B,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/prop-types/lib/has.js"], "sourcesContent": ["module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/prop-types/checkPropTypes.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GA4FK;AA1FN;AAEA,IAAI,eAAe,YAAY;AAE/B,wCAA2C;IACzC,IAAI;IACJ,IAAI,qBAAqB,CAAC;IAC1B,IAAI;IAEJ,eAAe,SAAS,IAAI;QAC1B,IAAI,UAAU,cAAc;QAC5B,IAAI,OAAO,YAAY,aAAa;YAClC,QAAQ,KAAK,CAAC;QAChB;QACA,IAAI;YACF,qCAAqC;YACrC,wEAAwE;YACxE,yDAAyD;YACzD,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,GAAG,CAAO;IACrB;AACF;AAEA;;;;;;;;;;CAUC,GACD,SAAS,eAAe,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ;IAC1E,wCAA2C;QACzC,IAAK,IAAI,gBAAgB,UAAW;YAClC,IAAI,IAAI,WAAW,eAAe;gBAChC,IAAI;gBACJ,oEAAoE;gBACpE,mEAAmE;gBACnE,0DAA0D;gBAC1D,IAAI;oBACF,qEAAqE;oBACrE,mEAAmE;oBACnE,IAAI,OAAO,SAAS,CAAC,aAAa,KAAK,YAAY;wBACjD,IAAI,MAAM,MACR,CAAC,iBAAiB,aAAa,IAAI,OAAO,WAAW,YAAY,eAAe,mBAChF,iFAAiF,OAAO,SAAS,CAAC,aAAa,GAAG,OAClH;wBAEF,IAAI,IAAI,GAAG;wBACX,MAAM;oBACR;oBACA,QAAQ,SAAS,CAAC,aAAa,CAAC,QAAQ,cAAc,eAAe,UAAU,MAAM;gBACvF,EAAE,OAAO,IAAI;oBACX,QAAQ;gBACV;gBACA,IAAI,SAAS,CAAC,CAAC,iBAAiB,KAAK,GAAG;oBACtC,aACE,CAAC,iBAAiB,aAAa,IAAI,6BACnC,WAAW,OAAO,eAAe,oCACjC,8DAA8D,OAAO,QAAQ,OAC7E,oEACA,mEACA;gBAEJ;gBACA,IAAI,iBAAiB,SAAS,CAAC,CAAC,MAAM,OAAO,IAAI,kBAAkB,GAAG;oBACpE,wEAAwE;oBACxE,cAAc;oBACd,kBAAkB,CAAC,MAAM,OAAO,CAAC,GAAG;oBAEpC,IAAI,QAAQ,WAAW,aAAa;oBAEpC,aACE,YAAY,WAAW,YAAY,MAAM,OAAO,GAAG,CAAC,SAAS,OAAO,QAAQ,EAAE;gBAElF;YACF;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,eAAe,iBAAiB,GAAG;IACjC,IAAI,oDAAyB,cAAc;QACzC,qBAAqB,CAAC;IACxB;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/prop-types/factoryWithTypeCheckers.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAsKO;AApKR;AAEA,IAAI;AACJ,IAAI;AAEJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,eAAe,YAAY;AAE/B,wCAA2C;IACzC,eAAe,SAAS,IAAI;QAC1B,IAAI,UAAU,cAAc;QAC5B,IAAI,OAAO,YAAY,aAAa;YAClC,QAAQ,KAAK,CAAC;QAChB;QACA,IAAI;YACF,qCAAqC;YACrC,wEAAwE;YACxE,yDAAyD;YACzD,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,GAAG,CAAC;IACf;AACF;AAEA,SAAS;IACP,OAAO;AACT;AAEA,OAAO,OAAO,GAAG,SAAS,cAAc,EAAE,mBAAmB;IAC3D,iBAAiB,GACjB,IAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO,QAAQ;IACrE,IAAI,uBAAuB,cAAc,sBAAsB;IAE/D;;;;;;;;;;;;;GAaC,GACD,SAAS,cAAc,aAAa;QAClC,IAAI,aAAa,iBAAiB,CAAC,mBAAmB,aAAa,CAAC,gBAAgB,IAAI,aAAa,CAAC,qBAAqB;QAC3H,IAAI,OAAO,eAAe,YAAY;YACpC,OAAO;QACT;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CC,GAED,IAAI,YAAY;IAEhB,aAAa;IACb,qFAAqF;IACrF,IAAI,iBAAiB;QACnB,OAAO,2BAA2B;QAClC,QAAQ,2BAA2B;QACnC,MAAM,2BAA2B;QACjC,MAAM,2BAA2B;QACjC,QAAQ,2BAA2B;QACnC,QAAQ,2BAA2B;QACnC,QAAQ,2BAA2B;QACnC,QAAQ,2BAA2B;QAEnC,KAAK;QACL,SAAS;QACT,SAAS;QACT,aAAa;QACb,YAAY;QACZ,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW;QACX,OAAO;QACP,OAAO;IACT;IAEA;;;GAGC,GACD,gCAAgC,GAChC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,sBAAsB;QACtB,IAAI,MAAM,GAAG;YACX,kBAAkB;YAClB,0BAA0B;YAC1B,OAAO,MAAM,KAAK,IAAI,MAAM,IAAI;QAClC,OAAO;YACL,uBAAuB;YACvB,OAAO,MAAM,KAAK,MAAM;QAC1B;IACF;IACA,+BAA+B,GAE/B;;;;;;GAMC,GACD,SAAS,cAAc,OAAO,EAAE,IAAI;QAClC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;QACtD,IAAI,CAAC,KAAK,GAAG;IACf;IACA,0DAA0D;IAC1D,cAAc,SAAS,GAAG,MAAM,SAAS;IAEzC,SAAS,2BAA2B,QAAQ;QAC1C,IAAI,oDAAyB,cAAc;YACzC,IAAI,0BAA0B,CAAC;YAC/B,IAAI,6BAA6B;QACnC;QACA,SAAS,UAAU,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM;YAC3F,gBAAgB,iBAAiB;YACjC,eAAe,gBAAgB;YAE/B,IAAI,WAAW,sBAAsB;gBACnC,IAAI,qBAAqB;oBACvB,sDAAsD;oBACtD,IAAI,MAAM,IAAI,MACZ,yFACA,oDACA;oBAEF,IAAI,IAAI,GAAG;oBACX,MAAM;gBACR,OAAO,IAAI,oDAAyB,gBAAgB,OAAO,YAAY,aAAa;oBAClF,gDAAgD;oBAChD,IAAI,WAAW,gBAAgB,MAAM;oBACrC,IACE,CAAC,uBAAuB,CAAC,SAAS,IAClC,0FAA0F;oBAC1F,6BAA6B,GAC7B;wBACA,aACE,2DACA,uBAAuB,eAAe,gBAAgB,gBAAgB,2BACtE,4DACA,mEACA,kEAAkE;wBAEpE,uBAAuB,CAAC,SAAS,GAAG;wBACpC;oBACF;gBACF;YACF;YACA,IAAI,KAAK,CAAC,SAAS,IAAI,MAAM;gBAC3B,IAAI,YAAY;oBACd,IAAI,KAAK,CAAC,SAAS,KAAK,MAAM;wBAC5B,OAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,6BAA6B,CAAC,SAAS,gBAAgB,6BAA6B;oBACzJ;oBACA,OAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,gCAAgC,CAAC,MAAM,gBAAgB,kCAAkC;gBAC9J;gBACA,OAAO;YACT,OAAO;gBACL,OAAO,SAAS,OAAO,UAAU,eAAe,UAAU;YAC5D;QACF;QAEA,IAAI,mBAAmB,UAAU,IAAI,CAAC,MAAM;QAC5C,iBAAiB,UAAU,GAAG,UAAU,IAAI,CAAC,MAAM;QAEnD,OAAO;IACT;IAEA,SAAS,2BAA2B,YAAY;QAC9C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM;YAC9E,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,WAAW,YAAY;YAC3B,IAAI,aAAa,cAAc;gBAC7B,qEAAqE;gBACrE,wEAAwE;gBACxE,sBAAsB;gBACtB,IAAI,cAAc,eAAe;gBAEjC,OAAO,IAAI,cACT,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,cAAc,oBAAoB,gBAAgB,cAAc,IAAI,CAAC,MAAM,eAAe,IAAI,GAClK;oBAAC,cAAc;gBAAY;YAE/B;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS;QACP,OAAO,2BAA2B;IACpC;IAEA,SAAS,yBAAyB,WAAW;QAC3C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,OAAO,gBAAgB,YAAY;gBACrC,OAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB;YAC9F;YACA,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY;gBAC7B,IAAI,WAAW,YAAY;gBAC3B,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,WAAW,oBAAoB,gBAAgB,uBAAuB;YACrK;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACzC,IAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK;gBAC7F,IAAI,iBAAiB,OAAO;oBAC1B,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS;QACP,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,CAAC,eAAe,YAAY;gBAC9B,IAAI,WAAW,YAAY;gBAC3B,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,WAAW,oBAAoB,gBAAgB,oCAAoC;YAClL;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS;QACP,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,CAAC,QAAQ,kBAAkB,CAAC,YAAY;gBAC1C,IAAI,WAAW,YAAY;gBAC3B,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,WAAW,oBAAoB,gBAAgB,yCAAyC;YACvL;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,0BAA0B,aAAa;QAC9C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,YAAY,aAAa,GAAG;gBAC/C,IAAI,oBAAoB,cAAc,IAAI,IAAI;gBAC9C,IAAI,kBAAkB,aAAa,KAAK,CAAC,SAAS;gBAClD,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,kBAAkB,oBAAoB,gBAAgB,cAAc,IAAI,CAAC,kBAAkB,oBAAoB,IAAI;YAClN;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,sBAAsB,cAAc;QAC3C,IAAI,CAAC,MAAM,OAAO,CAAC,iBAAiB;YAClC,wCAA2C;gBACzC,IAAI,UAAU,MAAM,GAAG,GAAG;oBACxB,aACE,iEAAiE,UAAU,MAAM,GAAG,iBACpF;gBAEJ,OAAO;oBACL,aAAa;gBACf;YACF;YACA,OAAO;QACT;QAEA,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;gBAC9C,IAAI,GAAG,WAAW,cAAc,CAAC,EAAE,GAAG;oBACpC,OAAO;gBACT;YACF;YAEA,IAAI,eAAe,KAAK,SAAS,CAAC,gBAAgB,SAAS,SAAS,GAAG,EAAE,KAAK;gBAC5E,IAAI,OAAO,eAAe;gBAC1B,IAAI,SAAS,UAAU;oBACrB,OAAO,OAAO;gBAChB;gBACA,OAAO;YACT;YACA,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,aAAa,OAAO,CAAC,kBAAkB,gBAAgB,wBAAwB,eAAe,GAAG;QAClM;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,0BAA0B,WAAW;QAC5C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,OAAO,gBAAgB,YAAY;gBACrC,OAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB;YAC9F;YACA,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,WAAW,YAAY;YAC3B,IAAI,aAAa,UAAU;gBACzB,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,eAAe,CAAC,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;YACtK;YACA,IAAK,IAAI,OAAO,UAAW;gBACzB,IAAI,IAAI,WAAW,MAAM;oBACvB,IAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK;oBAC3F,IAAI,iBAAiB,OAAO;wBAC1B,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,uBAAuB,mBAAmB;QACjD,IAAI,CAAC,MAAM,OAAO,CAAC,sBAAsB;YACvC,uCAAwC,aAAa;YACrD,OAAO;QACT;QAEA,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,MAAM,EAAE,IAAK;YACnD,IAAI,UAAU,mBAAmB,CAAC,EAAE;YACpC,IAAI,OAAO,YAAY,YAAY;gBACjC,aACE,uFACA,cAAc,yBAAyB,WAAW,eAAe,IAAI;gBAEvE,OAAO;YACT;QACF;QAEA,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,gBAAgB,EAAE;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,MAAM,EAAE,IAAK;gBACnD,IAAI,UAAU,mBAAmB,CAAC,EAAE;gBACpC,IAAI,gBAAgB,QAAQ,OAAO,UAAU,eAAe,UAAU,cAAc;gBACpF,IAAI,iBAAiB,MAAM;oBACzB,OAAO;gBACT;gBACA,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,IAAI,EAAE,iBAAiB;oBACjE,cAAc,IAAI,CAAC,cAAc,IAAI,CAAC,YAAY;gBACpD;YACF;YACA,IAAI,uBAAuB,AAAC,cAAc,MAAM,GAAG,IAAK,6BAA6B,cAAc,IAAI,CAAC,QAAQ,MAAK;YACrH,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,mBAAmB,CAAC,MAAM,gBAAgB,MAAM,uBAAuB,GAAG;QACnJ;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS;QACP,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,CAAC,OAAO,KAAK,CAAC,SAAS,GAAG;gBAC5B,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,mBAAmB,CAAC,MAAM,gBAAgB,0BAA0B;YAC7I;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,sBAAsB,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI;QAC7E,OAAO,IAAI,cACT,CAAC,iBAAiB,aAAa,IAAI,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,mBAC5F,iFAAiF,OAAO;IAE5F;IAEA,SAAS,uBAAuB,UAAU;QACxC,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,WAAW,YAAY;YAC3B,IAAI,aAAa,UAAU;gBACzB,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,OAAO,CAAC,kBAAkB,gBAAgB,uBAAuB;YACrK;YACA,IAAK,IAAI,OAAO,WAAY;gBAC1B,IAAI,UAAU,UAAU,CAAC,IAAI;gBAC7B,IAAI,OAAO,YAAY,YAAY;oBACjC,OAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe;gBAC1F;gBACA,IAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK;gBACvF,IAAI,OAAO;oBACT,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,OAAO,2BAA2B;IACpC;IAEA,SAAS,6BAA6B,UAAU;QAC9C,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;YACtE,IAAI,YAAY,KAAK,CAAC,SAAS;YAC/B,IAAI,WAAW,YAAY;YAC3B,IAAI,aAAa,UAAU;gBACzB,OAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,OAAO,CAAC,kBAAkB,gBAAgB,uBAAuB;YACrK;YACA,8EAA8E;YAC9E,IAAI,UAAU,OAAO,CAAC,GAAG,KAAK,CAAC,SAAS,EAAE;YAC1C,IAAK,IAAI,OAAO,QAAS;gBACvB,IAAI,UAAU,UAAU,CAAC,IAAI;gBAC7B,IAAI,IAAI,YAAY,QAAQ,OAAO,YAAY,YAAY;oBACzD,OAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe;gBAC1F;gBACA,IAAI,CAAC,SAAS;oBACZ,OAAO,IAAI,cACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,OACpG,mBAAmB,KAAK,SAAS,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,QACzD,mBAAmB,KAAK,SAAS,CAAC,OAAO,IAAI,CAAC,aAAa,MAAM;gBAErE;gBACA,IAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK;gBACvF,IAAI,OAAO;oBACT,OAAO;gBACT;YACF;YACA,OAAO;QACT;QAEA,OAAO,2BAA2B;IACpC;IAEA,SAAS,OAAO,SAAS;QACvB,OAAQ,OAAO;YACb,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC;YACV,KAAK;gBACH,IAAI,MAAM,OAAO,CAAC,YAAY;oBAC5B,OAAO,UAAU,KAAK,CAAC;gBACzB;gBACA,IAAI,cAAc,QAAQ,eAAe,YAAY;oBACnD,OAAO;gBACT;gBAEA,IAAI,aAAa,cAAc;gBAC/B,IAAI,YAAY;oBACd,IAAI,WAAW,WAAW,IAAI,CAAC;oBAC/B,IAAI;oBACJ,IAAI,eAAe,UAAU,OAAO,EAAE;wBACpC,MAAO,CAAC,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,IAAI,CAAE;4BACrC,IAAI,CAAC,OAAO,KAAK,KAAK,GAAG;gCACvB,OAAO;4BACT;wBACF;oBACF,OAAO;wBACL,+DAA+D;wBAC/D,MAAO,CAAC,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,IAAI,CAAE;4BACrC,IAAI,QAAQ,KAAK,KAAK;4BACtB,IAAI,OAAO;gCACT,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,GAAG;oCACrB,OAAO;gCACT;4BACF;wBACF;oBACF;gBACF,OAAO;oBACL,OAAO;gBACT;gBAEA,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,SAAS,SAAS,QAAQ,EAAE,SAAS;QACnC,iBAAiB;QACjB,IAAI,aAAa,UAAU;YACzB,OAAO;QACT;QAEA,gCAAgC;QAChC,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QAEA,wDAAwD;QACxD,IAAI,SAAS,CAAC,gBAAgB,KAAK,UAAU;YAC3C,OAAO;QACT;QAEA,gEAAgE;QAChE,IAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;YAC/D,OAAO;QACT;QAEA,OAAO;IACT;IAEA,yEAAyE;IACzE,SAAS,YAAY,SAAS;QAC5B,IAAI,WAAW,OAAO;QACtB,IAAI,MAAM,OAAO,CAAC,YAAY;YAC5B,OAAO;QACT;QACA,IAAI,qBAAqB,QAAQ;YAC/B,yEAAyE;YACzE,wEAAwE;YACxE,2BAA2B;YAC3B,OAAO;QACT;QACA,IAAI,SAAS,UAAU,YAAY;YACjC,OAAO;QACT;QACA,OAAO;IACT;IAEA,4EAA4E;IAC5E,oCAAoC;IACpC,SAAS,eAAe,SAAS;QAC/B,IAAI,OAAO,cAAc,eAAe,cAAc,MAAM;YAC1D,OAAO,KAAK;QACd;QACA,IAAI,WAAW,YAAY;QAC3B,IAAI,aAAa,UAAU;YACzB,IAAI,qBAAqB,MAAM;gBAC7B,OAAO;YACT,OAAO,IAAI,qBAAqB,QAAQ;gBACtC,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,yEAAyE;IACzE,8CAA8C;IAC9C,SAAS,yBAAyB,KAAK;QACrC,IAAI,OAAO,eAAe;QAC1B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,OAAO;YAChB;gBACE,OAAO;QACX;IACF;IAEA,4CAA4C;IAC5C,SAAS,aAAa,SAAS;QAC7B,IAAI,CAAC,UAAU,WAAW,IAAI,CAAC,UAAU,WAAW,CAAC,IAAI,EAAE;YACzD,OAAO;QACT;QACA,OAAO,UAAU,WAAW,CAAC,IAAI;IACnC;IAEA,eAAe,cAAc,GAAG;IAChC,eAAe,iBAAiB,GAAG,eAAe,iBAAiB;IACnE,eAAe,SAAS,GAAG;IAE3B,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1862, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/prop-types/index.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAEG;AAAJ,wCAA2C;IACzC,IAAI;IAEJ,iFAAiF;IACjF,kCAAkC;IAClC,IAAI,sBAAsB;IAC1B,OAAO,OAAO,GAAG,kHAAqC,QAAQ,SAAS,EAAE;AAC3E,OAAO;;AAIP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/date-arithmetic/index.js"], "sourcesContent": ["var MILI    = 'milliseconds'\n  , SECONDS = 'seconds'\n  , MINUTES = 'minutes'\n  , HOURS   = 'hours'\n  , DAY     = 'day'\n  , WEEK    = 'week'\n  , MONTH   = 'month'\n  , YEAR    = 'year'\n  , DECADE  = 'decade'\n  , CENTURY = 'century';\n\nvar multiplierMilli = {\n  'milliseconds': 1,\n  'seconds': 1000,\n  'minutes': 60 * 1000,\n  'hours': 60 * 60 * 1000,\n  'day': 24 * 60 * 60 * 1000,\n  'week': 7 * 24 * 60 * 60 * 1000 \n}\n\nvar multiplierMonth = {\n  'month': 1,\n  'year': 12,\n  'decade': 10 * 12,\n  'century': 100 * 12\n}\n\nfunction daysOf(year) {\n  return [31, daysInFeb(year), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]\n}\n\nfunction daysInFeb(year) {\n  return (\n      year % 4 === 0 \n      && year % 100 !== 0\n    ) || year % 400 === 0\n      ? 29\n      : 28\n}\n\nexport function add(d, num, unit) {\n  d = new Date(d)\n\n  switch (unit){\n    case MILI:\n    case SECONDS:\n    case MINUTES:\n    case HOURS:\n    case DAY:\n    case WEEK:\n      return addMillis(d, num * multiplierMilli[unit])\n    case MONTH:\n    case YEAR:\n    case DECADE:\n    case CENTURY:\n      return addMonths(d, num * multiplierMonth[unit])\n  }\n\n  throw new TypeError('Invalid units: \"' + unit + '\"')\n}\n\nfunction addMillis(d, num) {\n  var nextDate = new Date(+(d) + num)\n\n  return solveDST(d, nextDate)\n}\n\nfunction addMonths(d, num) {\n  var year = d.getFullYear()\n    , month = d.getMonth()\n    , day = d.getDate()\n    , totalMonths = year * 12 + month + num\n    , nextYear = Math.trunc(totalMonths / 12)\n    , nextMonth = totalMonths % 12\n    , nextDay = Math.min(day, daysOf(nextYear)[nextMonth])\n\n  var nextDate = new Date(d)\n  nextDate.setFullYear(nextYear)\n\n  // To avoid a bug when sets the Feb month\n  // with a date > 28 or date > 29 (leap year)\n  nextDate.setDate(1)\n\n  nextDate.setMonth(nextMonth)\n  nextDate.setDate(nextDay)\n\n  return nextDate\n}\n\nfunction solveDST(currentDate, nextDate) {\n  var currentOffset = currentDate.getTimezoneOffset()\n    , nextOffset = nextDate.getTimezoneOffset()\n\n  // if is DST, add the difference in minutes\n  // else the difference is zero\n  var diffMinutes = (nextOffset - currentOffset)\n\n  return new Date(+(nextDate) + diffMinutes * multiplierMilli['minutes'])\n}\n\nexport function subtract(d, num, unit) {\n  return add(d, -num, unit)\n}\n\nexport function startOf(d, unit, firstOfWeek) {\n  d = new Date(d)\n\n  switch (unit) {\n    case CENTURY:\n    case DECADE:\n    case YEAR:\n        d = month(d, 0);\n    case MONTH:\n        d = date(d, 1);\n    case WEEK:\n    case DAY:\n        d = hours(d, 0);\n    case HOURS:\n        d = minutes(d, 0);\n    case MINUTES:\n        d = seconds(d, 0);\n    case SECONDS:\n        d = milliseconds(d, 0);\n  }\n\n  if (unit === DECADE)\n    d = subtract(d, year(d) % 10, 'year')\n\n  if (unit === CENTURY)\n    d = subtract(d, year(d) % 100, 'year')\n\n  if (unit === WEEK)\n    d = weekday(d, 0, firstOfWeek);\n\n  return d\n}\n\nexport function endOf(d, unit, firstOfWeek){\n  d = new Date(d)\n  d = startOf(d, unit, firstOfWeek)\n  switch (unit) {\n    case CENTURY:\n    case DECADE:\n    case YEAR:\n    case MONTH:\n    case WEEK:\n      d = add(d, 1, unit)\n      d = subtract(d, 1, DAY)\n      d.setHours(23, 59, 59, 999)\n      break;\n    case DAY:\n      d.setHours(23, 59, 59, 999)\n      break;\n    case HOURS:\n    case MINUTES:\n    case SECONDS:\n      d = add(d, 1, unit)\n      d = subtract(d, 1, MILI)\n  }\n  return d\n}\n\nexport var eq =  createComparer(function(a, b){ return a === b })\nexport var neq = createComparer(function(a, b){ return a !== b })\nexport var gt =  createComparer(function(a, b){ return a > b })\nexport var gte = createComparer(function(a, b){ return a >= b })\nexport var lt =  createComparer(function(a, b){ return a < b })\nexport var lte = createComparer(function(a, b){ return a <= b })\n\nexport function min(){\n  return new Date(Math.min.apply(Math, arguments))\n}\n\nexport function max(){\n  return new Date(Math.max.apply(Math, arguments))\n}\n\nexport function inRange(day, min, max, unit){\n  unit = unit || 'day'\n\n  return (!min || gte(day, min, unit))\n      && (!max || lte(day, max, unit))\n}\n\nexport var milliseconds = createAccessor('Milliseconds')\nexport var seconds =      createAccessor('Seconds')\nexport var minutes =      createAccessor('Minutes')\nexport var hours =        createAccessor('Hours')\nexport var day =          createAccessor('Day')\nexport var date =         createAccessor('Date')\nexport var month =        createAccessor('Month')\nexport var year =         createAccessor('FullYear')\n\nexport function decade(d, val) {\n  return val === undefined\n    ? year(startOf(d, DECADE))\n    : add(d, val + 10, YEAR);\n}\n\nexport function century(d, val) {\n  return val === undefined\n    ? year(startOf(d, CENTURY))\n    : add(d, val + 100, YEAR);\n}\n\nexport function weekday(d, val, firstDay) {\n    var w = (day(d) + 7 - (firstDay || 0) ) % 7;\n\n    return val === undefined\n      ? w\n      : add(d, val - w, DAY);\n}\n\nexport function diff(date1, date2, unit, asFloat) {\n  var dividend, divisor, result;\n\n  switch (unit) {\n    case MILI:\n    case SECONDS:\n    case MINUTES:\n    case HOURS:\n    case DAY:\n    case WEEK:\n      dividend = date2.getTime() - date1.getTime(); break;\n    case MONTH:\n    case YEAR:\n    case DECADE:\n    case CENTURY:\n      dividend = (year(date2) - year(date1)) * 12 + month(date2) - month(date1); break;\n    default:\n      throw new TypeError('Invalid units: \"' + unit + '\"');\n  }\n\n  switch (unit) {\n    case MILI:\n        divisor = 1; break;\n    case SECONDS:\n        divisor = 1000; break;\n    case MINUTES:\n        divisor = 1000 * 60; break;\n    case HOURS:\n        divisor = 1000 * 60 * 60; break;\n    case DAY:\n        divisor = 1000 * 60 * 60 * 24; break;\n    case WEEK:\n        divisor = 1000 * 60 * 60 * 24 * 7; break;\n    case MONTH:\n        divisor = 1; break;\n    case YEAR:\n        divisor = 12; break;\n    case DECADE:\n        divisor = 120; break;\n    case CENTURY:\n        divisor = 1200; break;\n    default:\n      throw new TypeError('Invalid units: \"' + unit + '\"');\n  }\n\n  result = dividend / divisor;\n\n  return asFloat ? result : Math.round(result);\n}\n\nfunction createAccessor(method){\n  var hourLength = (function(method) {  \n    switch(method) {\n      case 'Milliseconds':\n        return 3600000;\n      case 'Seconds':\n        return 3600;\n      case 'Minutes':\n        return 60;\n      case 'Hours':\n        return 1;\n      default:\n        return null;\n    }\n  })(method);\n  \n  return function(d, val){\n    if (val === undefined)\n      return d['get' + method]()\n\n    var dateOut = new Date(d)\n    dateOut['set' + method](val)\n    \n    if(hourLength && dateOut['get'+method]() != val && (method === 'Hours' || val >=hourLength && (dateOut.getHours()-d.getHours()<Math.floor(val/hourLength))) ){\n      //Skip DST hour, if it occurs\n      dateOut['set'+method](val+hourLength);\n    }\n    \n    return dateOut\n  }\n}\n\nfunction createComparer(operator) {\n  return function (a, b, unit) {\n    return operator(+startOf(a, unit), +startOf(b, unit))\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,OAAU,gBACV,UAAU,WACV,UAAU,WACV,QAAU,SACV,MAAU,OACV,OAAU,QACV,QAAU,SACV,OAAU,QACV,SAAU,UACV,UAAU;AAEd,IAAI,kBAAkB;IACpB,gBAAgB;IAChB,WAAW;IACX,WAAW,KAAK;IAChB,SAAS,KAAK,KAAK;IACnB,OAAO,KAAK,KAAK,KAAK;IACtB,QAAQ,IAAI,KAAK,KAAK,KAAK;AAC7B;AAEA,IAAI,kBAAkB;IACpB,SAAS;IACT,QAAQ;IACR,UAAU,KAAK;IACf,WAAW,MAAM;AACnB;AAEA,SAAS,OAAO,IAAI;IAClB,OAAO;QAAC;QAAI,UAAU;QAAO;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;AACtE;AAEA,SAAS,UAAU,IAAI;IACrB,OAAO,AACH,OAAO,MAAM,KACV,OAAO,QAAQ,KACf,OAAO,QAAQ,IAChB,KACA;AACR;AAEO,SAAS,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI;IAC9B,IAAI,IAAI,KAAK;IAEb,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,UAAU,GAAG,MAAM,eAAe,CAAC,KAAK;QACjD,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,UAAU,GAAG,MAAM,eAAe,CAAC,KAAK;IACnD;IAEA,MAAM,IAAI,UAAU,qBAAqB,OAAO;AAClD;AAEA,SAAS,UAAU,CAAC,EAAE,GAAG;IACvB,IAAI,WAAW,IAAI,KAAK,CAAE,IAAK;IAE/B,OAAO,SAAS,GAAG;AACrB;AAEA,SAAS,UAAU,CAAC,EAAE,GAAG;IACvB,IAAI,OAAO,EAAE,WAAW,IACpB,QAAQ,EAAE,QAAQ,IAClB,MAAM,EAAE,OAAO,IACf,cAAc,OAAO,KAAK,QAAQ,KAClC,WAAW,KAAK,KAAK,CAAC,cAAc,KACpC,YAAY,cAAc,IAC1B,UAAU,KAAK,GAAG,CAAC,KAAK,OAAO,SAAS,CAAC,UAAU;IAEvD,IAAI,WAAW,IAAI,KAAK;IACxB,SAAS,WAAW,CAAC;IAErB,yCAAyC;IACzC,4CAA4C;IAC5C,SAAS,OAAO,CAAC;IAEjB,SAAS,QAAQ,CAAC;IAClB,SAAS,OAAO,CAAC;IAEjB,OAAO;AACT;AAEA,SAAS,SAAS,WAAW,EAAE,QAAQ;IACrC,IAAI,gBAAgB,YAAY,iBAAiB,IAC7C,aAAa,SAAS,iBAAiB;IAE3C,2CAA2C;IAC3C,8BAA8B;IAC9B,IAAI,cAAe,aAAa;IAEhC,OAAO,IAAI,KAAK,CAAE,WAAY,cAAc,eAAe,CAAC,UAAU;AACxE;AAEO,SAAS,SAAS,CAAC,EAAE,GAAG,EAAE,IAAI;IACnC,OAAO,IAAI,GAAG,CAAC,KAAK;AACtB;AAEO,SAAS,QAAQ,CAAC,EAAE,IAAI,EAAE,WAAW;IAC1C,IAAI,IAAI,KAAK;IAEb,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,MAAM,GAAG;QACjB,KAAK;YACD,IAAI,KAAK,GAAG;QAChB,KAAK;QACL,KAAK;YACD,IAAI,MAAM,GAAG;QACjB,KAAK;YACD,IAAI,QAAQ,GAAG;QACnB,KAAK;YACD,IAAI,QAAQ,GAAG;QACnB,KAAK;YACD,IAAI,aAAa,GAAG;IAC1B;IAEA,IAAI,SAAS,QACX,IAAI,SAAS,GAAG,KAAK,KAAK,IAAI;IAEhC,IAAI,SAAS,SACX,IAAI,SAAS,GAAG,KAAK,KAAK,KAAK;IAEjC,IAAI,SAAS,MACX,IAAI,QAAQ,GAAG,GAAG;IAEpB,OAAO;AACT;AAEO,SAAS,MAAM,CAAC,EAAE,IAAI,EAAE,WAAW;IACxC,IAAI,IAAI,KAAK;IACb,IAAI,QAAQ,GAAG,MAAM;IACrB,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,IAAI,IAAI,GAAG,GAAG;YACd,IAAI,SAAS,GAAG,GAAG;YACnB,EAAE,QAAQ,CAAC,IAAI,IAAI,IAAI;YACvB;QACF,KAAK;YACH,EAAE,QAAQ,CAAC,IAAI,IAAI,IAAI;YACvB;QACF,KAAK;QACL,KAAK;QACL,KAAK;YACH,IAAI,IAAI,GAAG,GAAG;YACd,IAAI,SAAS,GAAG,GAAG;IACvB;IACA,OAAO;AACT;AAEO,IAAI,KAAM,eAAe,SAAS,CAAC,EAAE,CAAC;IAAG,OAAO,MAAM;AAAE;AACxD,IAAI,MAAM,eAAe,SAAS,CAAC,EAAE,CAAC;IAAG,OAAO,MAAM;AAAE;AACxD,IAAI,KAAM,eAAe,SAAS,CAAC,EAAE,CAAC;IAAG,OAAO,IAAI;AAAE;AACtD,IAAI,MAAM,eAAe,SAAS,CAAC,EAAE,CAAC;IAAG,OAAO,KAAK;AAAE;AACvD,IAAI,KAAM,eAAe,SAAS,CAAC,EAAE,CAAC;IAAG,OAAO,IAAI;AAAE;AACtD,IAAI,MAAM,eAAe,SAAS,CAAC,EAAE,CAAC;IAAG,OAAO,KAAK;AAAE;AAEvD,SAAS;IACd,OAAO,IAAI,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM;AACvC;AAEO,SAAS;IACd,OAAO,IAAI,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM;AACvC;AAEO,SAAS,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;IACzC,OAAO,QAAQ;IAEf,OAAO,CAAC,CAAC,OAAO,IAAI,KAAK,KAAK,KAAK,KAC5B,CAAC,CAAC,OAAO,IAAI,KAAK,KAAK,KAAK;AACrC;AAEO,IAAI,eAAe,eAAe;AAClC,IAAI,UAAe,eAAe;AAClC,IAAI,UAAe,eAAe;AAClC,IAAI,QAAe,eAAe;AAClC,IAAI,MAAe,eAAe;AAClC,IAAI,OAAe,eAAe;AAClC,IAAI,QAAe,eAAe;AAClC,IAAI,OAAe,eAAe;AAElC,SAAS,OAAO,CAAC,EAAE,GAAG;IAC3B,OAAO,QAAQ,YACX,KAAK,QAAQ,GAAG,WAChB,IAAI,GAAG,MAAM,IAAI;AACvB;AAEO,SAAS,QAAQ,CAAC,EAAE,GAAG;IAC5B,OAAO,QAAQ,YACX,KAAK,QAAQ,GAAG,YAChB,IAAI,GAAG,MAAM,KAAK;AACxB;AAEO,SAAS,QAAQ,CAAC,EAAE,GAAG,EAAE,QAAQ;IACpC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC,CAAE,IAAI;IAE1C,OAAO,QAAQ,YACX,IACA,IAAI,GAAG,MAAM,GAAG;AACxB;AAEO,SAAS,KAAK,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO;IAC9C,IAAI,UAAU,SAAS;IAEvB,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,WAAW,MAAM,OAAO,KAAK,MAAM,OAAO;YAAI;QAChD,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,WAAW,CAAC,KAAK,SAAS,KAAK,MAAM,IAAI,KAAK,MAAM,SAAS,MAAM;YAAQ;QAC7E;YACE,MAAM,IAAI,UAAU,qBAAqB,OAAO;IACpD;IAEA,OAAQ;QACN,KAAK;YACD,UAAU;YAAG;QACjB,KAAK;YACD,UAAU;YAAM;QACpB,KAAK;YACD,UAAU,OAAO;YAAI;QACzB,KAAK;YACD,UAAU,OAAO,KAAK;YAAI;QAC9B,KAAK;YACD,UAAU,OAAO,KAAK,KAAK;YAAI;QACnC,KAAK;YACD,UAAU,OAAO,KAAK,KAAK,KAAK;YAAG;QACvC,KAAK;YACD,UAAU;YAAG;QACjB,KAAK;YACD,UAAU;YAAI;QAClB,KAAK;YACD,UAAU;YAAK;QACnB,KAAK;YACD,UAAU;YAAM;QACpB;YACE,MAAM,IAAI,UAAU,qBAAqB,OAAO;IACpD;IAEA,SAAS,WAAW;IAEpB,OAAO,UAAU,SAAS,KAAK,KAAK,CAAC;AACvC;AAEA,SAAS,eAAe,MAAM;IAC5B,IAAI,aAAa,AAAC,SAAS,MAAM;QAC/B,OAAO;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF,EAAG;IAEH,OAAO,SAAS,CAAC,EAAE,GAAG;QACpB,IAAI,QAAQ,WACV,OAAO,CAAC,CAAC,QAAQ,OAAO;QAE1B,IAAI,UAAU,IAAI,KAAK;QACvB,OAAO,CAAC,QAAQ,OAAO,CAAC;QAExB,IAAG,cAAc,OAAO,CAAC,QAAM,OAAO,MAAM,OAAO,CAAC,WAAW,WAAW,OAAM,cAAe,QAAQ,QAAQ,KAAG,EAAE,QAAQ,KAAG,KAAK,KAAK,CAAC,MAAI,WAAY,GAAG;YAC3J,6BAA6B;YAC7B,OAAO,CAAC,QAAM,OAAO,CAAC,MAAI;QAC5B;QAEA,OAAO;IACT;AACF;AAEA,SAAS,eAAe,QAAQ;IAC9B,OAAO,SAAU,CAAC,EAAE,CAAC,EAAE,IAAI;QACzB,OAAO,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,GAAG;IACjD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/ownerDocument.js"], "sourcesContent": ["/**\n * Returns the owner document of a given element.\n * \n * @param node the element\n */\nexport default function ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACc,SAAS,cAAc,IAAI;IACxC,OAAO,QAAQ,KAAK,aAAa,IAAI;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/ownerWindow.js"], "sourcesContent": ["import ownerDocument from './ownerDocument';\n/**\n * Returns the owner window of a given element.\n * \n * @param node the element\n */\n\nexport default function ownerWindow(node) {\n  var doc = ownerDocument(node);\n  return doc && doc.defaultView || window;\n}"], "names": [], "mappings": ";;;AAAA;;AAOe,SAAS,YAAY,IAAI;IACtC,IAAI,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAa,AAAD,EAAE;IACxB,OAAO,OAAO,IAAI,WAAW,IAAI;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/getComputedStyle.js"], "sourcesContent": ["import ownerWindow from './ownerWindow';\n/**\n * Returns one or all computed style properties of an element.\n * \n * @param node the element\n * @param psuedoElement the style property\n */\n\nexport default function getComputedStyle(node, psuedoElement) {\n  return ownerWindow(node).getComputedStyle(node, psuedoElement);\n}"], "names": [], "mappings": ";;;AAAA;;AAQe,SAAS,iBAAiB,IAAI,EAAE,aAAa;IAC1D,OAAO,CAAA,GAAA,uJAAA,CAAA,UAAW,AAAD,EAAE,MAAM,gBAAgB,CAAC,MAAM;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/hyphenate.js"], "sourcesContent": ["var rUpper = /([A-Z])/g;\nexport default function hyphenate(string) {\n  return string.replace(rUpper, '-$1').toLowerCase();\n}"], "names": [], "mappings": ";;;AAAA,IAAI,SAAS;AACE,SAAS,UAAU,MAAM;IACtC,OAAO,OAAO,OAAO,CAAC,QAAQ,OAAO,WAAW;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/hyphenateStyle.js"], "sourcesContent": ["/**\n * Copyright 2013-2014, Facebook, Inc.\n * All rights reserved.\n * https://github.com/facebook/react/blob/2aeb8a2a6beb00617a4217f7f8284924fa2ad819/src/vendor/core/hyphenateStyleName.js\n */\nimport hyphenate from './hyphenate';\nvar msPattern = /^ms-/;\nexport default function hyphenateStyleName(string) {\n  return hyphenate(string).replace(msPattern, '-ms-');\n}"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACD;;AACA,IAAI,YAAY;AACD,SAAS,mBAAmB,MAAM;IAC/C,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAS,AAAD,EAAE,QAAQ,OAAO,CAAC,WAAW;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/isTransform.js"], "sourcesContent": ["var supportedTransforms = /^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;\nexport default function isTransform(value) {\n  return !!(value && supportedTransforms.test(value));\n}"], "names": [], "mappings": ";;;AAAA,IAAI,sBAAsB;AACX,SAAS,YAAY,KAAK;IACvC,OAAO,CAAC,CAAC,CAAC,SAAS,oBAAoB,IAAI,CAAC,MAAM;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/css.js"], "sourcesContent": ["import getComputedStyle from './getComputedStyle';\nimport hyphenate from './hyphenateStyle';\nimport isTransform from './isTransform';\n\nfunction style(node, property) {\n  var css = '';\n  var transforms = '';\n\n  if (typeof property === 'string') {\n    return node.style.getPropertyValue(hyphenate(property)) || getComputedStyle(node).getPropertyValue(hyphenate(property));\n  }\n\n  Object.keys(property).forEach(function (key) {\n    var value = property[key];\n\n    if (!value && value !== 0) {\n      node.style.removeProperty(hyphenate(key));\n    } else if (isTransform(key)) {\n      transforms += key + \"(\" + value + \") \";\n    } else {\n      css += hyphenate(key) + \": \" + value + \";\";\n    }\n  });\n\n  if (transforms) {\n    css += \"transform: \" + transforms + \";\";\n  }\n\n  node.style.cssText += \";\" + css;\n}\n\nexport default style;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,MAAM,IAAI,EAAE,QAAQ;IAC3B,IAAI,MAAM;IACV,IAAI,aAAa;IAEjB,IAAI,OAAO,aAAa,UAAU;QAChC,OAAO,KAAK,KAAK,CAAC,gBAAgB,CAAC,CAAA,GAAA,0JAAA,CAAA,UAAS,AAAD,EAAE,cAAc,CAAA,GAAA,4JAAA,CAAA,UAAgB,AAAD,EAAE,MAAM,gBAAgB,CAAC,CAAA,GAAA,0JAAA,CAAA,UAAS,AAAD,EAAE;IAC/G;IAEA,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,SAAU,GAAG;QACzC,IAAI,QAAQ,QAAQ,CAAC,IAAI;QAEzB,IAAI,CAAC,SAAS,UAAU,GAAG;YACzB,KAAK,KAAK,CAAC,cAAc,CAAC,CAAA,GAAA,0JAAA,CAAA,UAAS,AAAD,EAAE;QACtC,OAAO,IAAI,CAAA,GAAA,uJAAA,CAAA,UAAW,AAAD,EAAE,MAAM;YAC3B,cAAc,MAAM,MAAM,QAAQ;QACpC,OAAO;YACL,OAAO,CAAA,GAAA,0JAAA,CAAA,UAAS,AAAD,EAAE,OAAO,OAAO,QAAQ;QACzC;IACF;IAEA,IAAI,YAAY;QACd,OAAO,gBAAgB,aAAa;IACtC;IAEA,KAAK,KAAK,CAAC,OAAO,IAAI,MAAM;AAC9B;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/contains.js"], "sourcesContent": ["/* eslint-disable no-bitwise, no-cond-assign */\n\n/**\n * Checks if an element contains another given element.\n * \n * @param context the context element\n * @param node the element to check\n */\nexport default function contains(context, node) {\n  // HTML DOM and SVG DOM may have different support levels,\n  // so we need to check on context instead of a document root element.\n  if (context.contains) return context.contains(node);\n  if (context.compareDocumentPosition) return context === node || !!(context.compareDocumentPosition(node) & 16);\n}"], "names": [], "mappings": "AAAA,6CAA6C,GAE7C;;;;;CAKC;;;AACc,SAAS,SAAS,OAAO,EAAE,IAAI;IAC5C,0DAA0D;IAC1D,qEAAqE;IACrE,IAAI,QAAQ,QAAQ,EAAE,OAAO,QAAQ,QAAQ,CAAC;IAC9C,IAAI,QAAQ,uBAAuB,EAAE,OAAO,YAAY,QAAQ,CAAC,CAAC,CAAC,QAAQ,uBAAuB,CAAC,QAAQ,EAAE;AAC/G", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/isDocument.js"], "sourcesContent": ["export default function isDocument(element) {\n  return 'nodeType' in element && element.nodeType === document.DOCUMENT_NODE;\n}"], "names": [], "mappings": ";;;AAAe,SAAS,WAAW,OAAO;IACxC,OAAO,cAAc,WAAW,QAAQ,QAAQ,KAAK,SAAS,aAAa;AAC7E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/isWindow.js"], "sourcesContent": ["import isDocument from './isDocument';\nexport default function isWindow(node) {\n  if ('window' in node && node.window === node) return node;\n  if (isDocument(node)) return node.defaultView || false;\n  return false;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,SAAS,IAAI;IACnC,IAAI,YAAY,QAAQ,KAAK,MAAM,KAAK,MAAM,OAAO;IACrD,IAAI,CAAA,GAAA,sJAAA,CAAA,UAAU,AAAD,EAAE,OAAO,OAAO,KAAK,WAAW,IAAI;IACjD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/getScrollAccessor.js"], "sourcesContent": ["import isWindow from './isWindow';\nexport default function getscrollAccessor(offset) {\n  var prop = offset === 'pageXOffset' ? 'scrollLeft' : 'scrollTop';\n\n  function scrollAccessor(node, val) {\n    var win = isWindow(node);\n\n    if (val === undefined) {\n      return win ? win[offset] : node[prop];\n    }\n\n    if (win) {\n      win.scrollTo(win[offset], val);\n    } else {\n      node[prop] = val;\n    }\n  }\n\n  return scrollAccessor;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,kBAAkB,MAAM;IAC9C,IAAI,OAAO,WAAW,gBAAgB,eAAe;IAErD,SAAS,eAAe,IAAI,EAAE,GAAG;QAC/B,IAAI,MAAM,CAAA,GAAA,oJAAA,CAAA,UAAQ,AAAD,EAAE;QAEnB,IAAI,QAAQ,WAAW;YACrB,OAAO,MAAM,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK;QACvC;QAEA,IAAI,KAAK;YACP,IAAI,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE;QAC5B,OAAO;YACL,IAAI,CAAC,KAAK,GAAG;QACf;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/scrollLeft.js"], "sourcesContent": ["import getScrollAccessor from './getScrollAccessor';\n/**\n * Gets or sets the scroll left position of a given element.\n * \n * @param node the element\n * @param val the position to set\n */\n\nexport default getScrollAccessor('pageXOffset');"], "names": [], "mappings": ";;;AAAA;;uCAQe,CAAA,GAAA,6JAAA,CAAA,UAAiB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/scrollTop.js"], "sourcesContent": ["import getScrollAccessor from './getScrollAccessor';\n/**\n * Gets or sets the scroll top position of a given element.\n * \n * @param node the element\n * @param val the position to set\n */\n\nexport default getScrollAccessor('pageYOffset');"], "names": [], "mappings": ";;;AAAA;;uCAQe,CAAA,GAAA,6JAAA,CAAA,UAAiB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/offset.js"], "sourcesContent": ["import contains from './contains';\nimport ownerDocument from './ownerDocument';\nimport scrollLeft from './scrollLeft';\nimport scrollTop from './scrollTop';\n/**\n * Returns the offset of a given element, including top and left positions, width and height.\n * \n * @param node the element\n */\n\nexport default function offset(node) {\n  var doc = ownerDocument(node);\n  var box = {\n    top: 0,\n    left: 0,\n    height: 0,\n    width: 0\n  };\n  var docElem = doc && doc.documentElement; // Make sure it's not a disconnected DOM node\n\n  if (!docElem || !contains(docElem, node)) return box;\n  if (node.getBoundingClientRect !== undefined) box = node.getBoundingClientRect();\n  box = {\n    top: box.top + scrollTop(docElem) - (docElem.clientTop || 0),\n    left: box.left + scrollLeft(docElem) - (docElem.clientLeft || 0),\n    width: box.width,\n    height: box.height\n  };\n  return box;\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAOe,SAAS,OAAO,IAAI;IACjC,IAAI,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAa,AAAD,EAAE;IACxB,IAAI,MAAM;QACR,KAAK;QACL,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA,IAAI,UAAU,OAAO,IAAI,eAAe,EAAE,6CAA6C;IAEvF,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,oJAAA,CAAA,UAAQ,AAAD,EAAE,SAAS,OAAO,OAAO;IACjD,IAAI,KAAK,qBAAqB,KAAK,WAAW,MAAM,KAAK,qBAAqB;IAC9E,MAAM;QACJ,KAAK,IAAI,GAAG,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAS,AAAD,EAAE,WAAW,CAAC,QAAQ,SAAS,IAAI,CAAC;QAC3D,MAAM,IAAI,IAAI,GAAG,CAAA,GAAA,sJAAA,CAAA,UAAU,AAAD,EAAE,WAAW,CAAC,QAAQ,UAAU,IAAI,CAAC;QAC/D,OAAO,IAAI,KAAK;QAChB,QAAQ,IAAI,MAAM;IACpB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/offsetParent.js"], "sourcesContent": ["import css from './css';\nimport ownerDocument from './ownerDocument';\n\nvar isHTMLElement = function isHTMLElement(e) {\n  return !!e && 'offsetParent' in e;\n};\n\nexport default function offsetParent(node) {\n  var doc = ownerDocument(node);\n  var parent = node && node.offsetParent;\n\n  while (isHTMLElement(parent) && parent.nodeName !== 'HTML' && css(parent, 'position') === 'static') {\n    parent = parent.offsetParent;\n  }\n\n  return parent || doc.documentElement;\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,IAAI,gBAAgB,SAAS,cAAc,CAAC;IAC1C,OAAO,CAAC,CAAC,KAAK,kBAAkB;AAClC;AAEe,SAAS,aAAa,IAAI;IACvC,IAAI,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAa,AAAD,EAAE;IACxB,IAAI,SAAS,QAAQ,KAAK,YAAY;IAEtC,MAAO,cAAc,WAAW,OAAO,QAAQ,KAAK,UAAU,CAAA,GAAA,+IAAA,CAAA,UAAG,AAAD,EAAE,QAAQ,gBAAgB,SAAU;QAClG,SAAS,OAAO,YAAY;IAC9B;IAEA,OAAO,UAAU,IAAI,eAAe;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2460, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/position.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport css from './css';\nimport getOffset from './offset';\nimport getOffsetParent from './offsetParent';\nimport scrollLeft from './scrollLeft';\nimport scrollTop from './scrollTop';\n\nvar nodeName = function nodeName(node) {\n  return node.nodeName && node.nodeName.toLowerCase();\n};\n/**\n * Returns the relative position of a given element.\n * \n * @param node the element\n * @param offsetParent the offset parent\n */\n\n\nexport default function position(node, offsetParent) {\n  var parentOffset = {\n    top: 0,\n    left: 0\n  };\n  var offset; // Fixed elements are offset from window (parentOffset = {top:0, left: 0},\n  // because it is its only offset parent\n\n  if (css(node, 'position') === 'fixed') {\n    offset = node.getBoundingClientRect();\n  } else {\n    var parent = offsetParent || getOffsetParent(node);\n    offset = getOffset(node);\n    if (nodeName(parent) !== 'html') parentOffset = getOffset(parent);\n    var borderTop = String(css(parent, 'borderTopWidth') || 0);\n    parentOffset.top += parseInt(borderTop, 10) - scrollTop(parent) || 0;\n    var borderLeft = String(css(parent, 'borderLeftWidth') || 0);\n    parentOffset.left += parseInt(borderLeft, 10) - scrollLeft(parent) || 0;\n  }\n\n  var marginTop = String(css(node, 'marginTop') || 0);\n  var marginLeft = String(css(node, 'marginLeft') || 0); // Subtract parent offsets and node margins\n\n  return _extends({}, offset, {\n    top: offset.top - parentOffset.top - (parseInt(marginTop, 10) || 0),\n    left: offset.left - parentOffset.left - (parseInt(marginLeft, 10) || 0)\n  });\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,IAAI,WAAW,SAAS,SAAS,IAAI;IACnC,OAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,WAAW;AACnD;AASe,SAAS,SAAS,IAAI,EAAE,YAAY;IACjD,IAAI,eAAe;QACjB,KAAK;QACL,MAAM;IACR;IACA,IAAI,QAAQ,0EAA0E;IACtF,uCAAuC;IAEvC,IAAI,CAAA,GAAA,+IAAA,CAAA,UAAG,AAAD,EAAE,MAAM,gBAAgB,SAAS;QACrC,SAAS,KAAK,qBAAqB;IACrC,OAAO;QACL,IAAI,SAAS,gBAAgB,CAAA,GAAA,wJAAA,CAAA,UAAe,AAAD,EAAE;QAC7C,SAAS,CAAA,GAAA,kJAAA,CAAA,UAAS,AAAD,EAAE;QACnB,IAAI,SAAS,YAAY,QAAQ,eAAe,CAAA,GAAA,kJAAA,CAAA,UAAS,AAAD,EAAE;QAC1D,IAAI,YAAY,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAG,AAAD,EAAE,QAAQ,qBAAqB;QACxD,aAAa,GAAG,IAAI,SAAS,WAAW,MAAM,CAAA,GAAA,qJAAA,CAAA,UAAS,AAAD,EAAE,WAAW;QACnE,IAAI,aAAa,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAG,AAAD,EAAE,QAAQ,sBAAsB;QAC1D,aAAa,IAAI,IAAI,SAAS,YAAY,MAAM,CAAA,GAAA,sJAAA,CAAA,UAAU,AAAD,EAAE,WAAW;IACxE;IAEA,IAAI,YAAY,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAG,AAAD,EAAE,MAAM,gBAAgB;IACjD,IAAI,aAAa,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAG,AAAD,EAAE,MAAM,iBAAiB,IAAI,2CAA2C;IAElG,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,QAAQ;QAC1B,KAAK,OAAO,GAAG,GAAG,aAAa,GAAG,GAAG,CAAC,SAAS,WAAW,OAAO,CAAC;QAClE,MAAM,OAAO,IAAI,GAAG,aAAa,IAAI,GAAG,CAAC,SAAS,YAAY,OAAO,CAAC;IACxE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/canUseDOM.js"], "sourcesContent": ["export default !!(typeof window !== 'undefined' && window.document && window.document.createElement);"], "names": [], "mappings": ";;;uCAAe,CAAC,CAAC,CAAC,OAAO,WAAW,eAAe,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/animationFrame.js"], "sourcesContent": ["import canUseDOM from './canUseDOM';\n\n/* https://github.com/component/raf */\nvar prev = new Date().getTime();\n\nfunction fallback(fn) {\n  var curr = new Date().getTime();\n  var ms = Math.max(0, 16 - (curr - prev));\n  var handle = setTimeout(fn, ms);\n  prev = curr;\n  return handle;\n}\n\nvar vendors = ['', 'webkit', 'moz', 'o', 'ms'];\nvar cancelMethod = 'clearTimeout';\nvar rafImpl = fallback; // eslint-disable-next-line import/no-mutable-exports\n\nvar getKey = function getKey(vendor, k) {\n  return vendor + (!vendor ? k : k[0].toUpperCase() + k.substr(1)) + \"AnimationFrame\";\n};\n\nif (canUseDOM) {\n  vendors.some(function (vendor) {\n    var rafMethod = getKey(vendor, 'request');\n\n    if (rafMethod in window) {\n      cancelMethod = getKey(vendor, 'cancel'); // @ts-ignore\n\n      rafImpl = function rafImpl(cb) {\n        return window[rafMethod](cb);\n      };\n    }\n\n    return !!rafImpl;\n  });\n}\n\nexport var cancel = function cancel(id) {\n  // @ts-ignore\n  if (typeof window[cancelMethod] === 'function') window[cancelMethod](id);\n};\nexport var request = rafImpl;"], "names": [], "mappings": ";;;;AAAA;;AAEA,oCAAoC,GACpC,IAAI,OAAO,IAAI,OAAO,OAAO;AAE7B,SAAS,SAAS,EAAE;IAClB,IAAI,OAAO,IAAI,OAAO,OAAO;IAC7B,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,IAAI;IACtC,IAAI,SAAS,WAAW,IAAI;IAC5B,OAAO;IACP,OAAO;AACT;AAEA,IAAI,UAAU;IAAC;IAAI;IAAU;IAAO;IAAK;CAAK;AAC9C,IAAI,eAAe;AACnB,IAAI,UAAU,UAAU,qDAAqD;AAE7E,IAAI,SAAS,SAAS,OAAO,MAAM,EAAE,CAAC;IACpC,OAAO,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,WAAW,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI;AACrE;AAEA,IAAI,qJAAA,CAAA,UAAS,EAAE;IACb,QAAQ,IAAI,CAAC,SAAU,MAAM;QAC3B,IAAI,YAAY,OAAO,QAAQ;QAE/B,IAAI,aAAa,QAAQ;YACvB,eAAe,OAAO,QAAQ,WAAW,aAAa;YAEtD,UAAU,SAAS,QAAQ,EAAE;gBAC3B,OAAO,MAAM,CAAC,UAAU,CAAC;YAC3B;QACF;QAEA,OAAO,CAAC,CAAC;IACX;AACF;AAEO,IAAI,SAAS,SAAS,OAAO,EAAE;IACpC,aAAa;IACb,IAAI,OAAO,MAAM,CAAC,aAAa,KAAK,YAAY,MAAM,CAAC,aAAa,CAAC;AACvE;AACO,IAAI,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/addEventListener.js"], "sourcesContent": ["/* eslint-disable no-return-assign */\nimport canUseDOM from './canUseDOM';\nexport var optionsSupported = false;\nexport var onceSupported = false;\n\ntry {\n  var options = {\n    get passive() {\n      return optionsSupported = true;\n    },\n\n    get once() {\n      // eslint-disable-next-line no-multi-assign\n      return onceSupported = optionsSupported = true;\n    }\n\n  };\n\n  if (canUseDOM) {\n    window.addEventListener('test', options, options);\n    window.removeEventListener('test', options, true);\n  }\n} catch (e) {\n  /* */\n}\n\n/**\n * An `addEventListener` ponyfill, supports the `once` option\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction addEventListener(node, eventName, handler, options) {\n  if (options && typeof options !== 'boolean' && !onceSupported) {\n    var once = options.once,\n        capture = options.capture;\n    var wrappedHandler = handler;\n\n    if (!onceSupported && once) {\n      wrappedHandler = handler.__once || function onceHandler(event) {\n        this.removeEventListener(eventName, onceHandler, capture);\n        handler.call(this, event);\n      };\n\n      handler.__once = wrappedHandler;\n    }\n\n    node.addEventListener(eventName, wrappedHandler, optionsSupported ? options : capture);\n  }\n\n  node.addEventListener(eventName, handler, options);\n}\n\nexport default addEventListener;"], "names": [], "mappings": "AAAA,mCAAmC;;;;;AACnC;;AACO,IAAI,mBAAmB;AACvB,IAAI,gBAAgB;AAE3B,IAAI;IACF,IAAI,UAAU;QACZ,IAAI,WAAU;YACZ,OAAO,mBAAmB;QAC5B;QAEA,IAAI,QAAO;YACT,2CAA2C;YAC3C,OAAO,gBAAgB,mBAAmB;QAC5C;IAEF;IAEA,IAAI,qJAAA,CAAA,UAAS,EAAE;QACb,OAAO,gBAAgB,CAAC,QAAQ,SAAS;QACzC,OAAO,mBAAmB,CAAC,QAAQ,SAAS;IAC9C;AACF,EAAE,OAAO,GAAG;AACV,GAAG,GACL;AAEA;;;;;;;CAOC,GACD,SAAS,iBAAiB,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO;IACzD,IAAI,WAAW,OAAO,YAAY,aAAa,CAAC,eAAe;QAC7D,IAAI,OAAO,QAAQ,IAAI,EACnB,UAAU,QAAQ,OAAO;QAC7B,IAAI,iBAAiB;QAErB,IAAI,CAAC,iBAAiB,MAAM;YAC1B,iBAAiB,QAAQ,MAAM,IAAI,SAAS,YAAY,KAAK;gBAC3D,IAAI,CAAC,mBAAmB,CAAC,WAAW,aAAa;gBACjD,QAAQ,IAAI,CAAC,IAAI,EAAE;YACrB;YAEA,QAAQ,MAAM,GAAG;QACnB;QAEA,KAAK,gBAAgB,CAAC,WAAW,gBAAgB,mBAAmB,UAAU;IAChF;IAEA,KAAK,gBAAgB,CAAC,WAAW,SAAS;AAC5C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/removeEventListener.js"], "sourcesContent": ["/**\n * A `removeEventListener` ponyfill\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction removeEventListener(node, eventName, handler, options) {\n  var capture = options && typeof options !== 'boolean' ? options.capture : options;\n  node.removeEventListener(eventName, handler, capture);\n\n  if (handler.__once) {\n    node.removeEventListener(eventName, handler.__once, capture);\n  }\n}\n\nexport default removeEventListener;"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,SAAS,oBAAoB,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO;IAC5D,IAAI,UAAU,WAAW,OAAO,YAAY,YAAY,QAAQ,OAAO,GAAG;IAC1E,KAAK,mBAAmB,CAAC,WAAW,SAAS;IAE7C,IAAI,QAAQ,MAAM,EAAE;QAClB,KAAK,mBAAmB,CAAC,WAAW,QAAQ,MAAM,EAAE;IACtD;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/listen.js"], "sourcesContent": ["import addEventListener from './addEventListener';\nimport removeEventListener from './removeEventListener';\n\nfunction listen(node, eventName, handler, options) {\n  addEventListener(node, eventName, handler, options);\n  return function () {\n    removeEventListener(node, eventName, handler, options);\n  };\n}\n\nexport default listen;"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,OAAO,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO;IAC/C,CAAA,GAAA,4JAAA,CAAA,UAAgB,AAAD,EAAE,MAAM,WAAW,SAAS;IAC3C,OAAO;QACL,CAAA,GAAA,+JAAA,CAAA,UAAmB,AAAD,EAAE,MAAM,WAAW,SAAS;IAChD;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/height.js"], "sourcesContent": ["import getWindow from './isWindow';\nimport offset from './offset';\n/**\n * Returns the height of a given element.\n * \n * @param node the element\n * @param client whether to use `clientHeight` if possible\n */\n\nexport default function height(node, client) {\n  var win = getWindow(node);\n  return win ? win.innerHeight : client ? node.clientHeight : offset(node).height;\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAQe,SAAS,OAAO,IAAI,EAAE,MAAM;IACzC,IAAI,MAAM,CAAA,GAAA,oJAAA,CAAA,UAAS,AAAD,EAAE;IACpB,OAAO,MAAM,IAAI,WAAW,GAAG,SAAS,KAAK,YAAY,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAM,AAAD,EAAE,MAAM,MAAM;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/querySelectorAll.js"], "sourcesContent": ["var toArray = Function.prototype.bind.call(Function.prototype.call, [].slice);\n/**\n * Runs `querySelectorAll` on a given element.\n * \n * @param element the element\n * @param selector the selector\n */\n\nexport default function qsa(element, selector) {\n  return toArray(element.querySelectorAll(selector));\n}"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU,SAAS,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK;AAQ7D,SAAS,IAAI,OAAO,EAAE,QAAQ;IAC3C,OAAO,QAAQ,QAAQ,gBAAgB,CAAC;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2696, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/matches.js"], "sourcesContent": ["var matchesImpl;\n/**\n * Checks if a given element matches a selector.\n * \n * @param node the element\n * @param selector the selector\n */\n\nexport default function matches(node, selector) {\n  if (!matchesImpl) {\n    var body = document.body;\n    var nativeMatch = body.matches || body.matchesSelector || body.webkitMatchesSelector || body.mozMatchesSelector || body.msMatchesSelector;\n\n    matchesImpl = function matchesImpl(n, s) {\n      return nativeMatch.call(n, s);\n    };\n  }\n\n  return matchesImpl(node, selector);\n}"], "names": [], "mappings": ";;;AAAA,IAAI;AAQW,SAAS,QAAQ,IAAI,EAAE,QAAQ;IAC5C,IAAI,CAAC,aAAa;QAChB,IAAI,OAAO,SAAS,IAAI;QACxB,IAAI,cAAc,KAAK,OAAO,IAAI,KAAK,eAAe,IAAI,KAAK,qBAAqB,IAAI,KAAK,kBAAkB,IAAI,KAAK,iBAAiB;QAEzI,cAAc,SAAS,YAAY,CAAC,EAAE,CAAC;YACrC,OAAO,YAAY,IAAI,CAAC,GAAG;QAC7B;IACF;IAEA,OAAO,YAAY,MAAM;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2716, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/closest.js"], "sourcesContent": ["import matches from './matches';\n/**\n * Returns the closest parent element that matches a given selector.\n * \n * @param node the reference element\n * @param selector the selector to match\n * @param stopAt stop traversing when this element is found\n */\n\nexport default function closest(node, selector, stopAt) {\n  if (node.closest && !stopAt) node.closest(selector);\n  var nextNode = node;\n\n  do {\n    if (matches(nextNode, selector)) return nextNode;\n    nextNode = nextNode.parentElement;\n  } while (nextNode && nextNode !== stopAt && nextNode.nodeType === document.ELEMENT_NODE);\n\n  return null;\n}"], "names": [], "mappings": ";;;AAAA;;AASe,SAAS,QAAQ,IAAI,EAAE,QAAQ,EAAE,MAAM;IACpD,IAAI,KAAK,OAAO,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC;IAC1C,IAAI,WAAW;IAEf,GAAG;QACD,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EAAE,UAAU,WAAW,OAAO;QACxC,WAAW,SAAS,aAAa;IACnC,QAAS,YAAY,aAAa,UAAU,SAAS,QAAQ,KAAK,SAAS,YAAY,CAAE;IAEzF,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2736, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/width.js"], "sourcesContent": ["import getWindow from './isWindow';\nimport offset from './offset';\n/**\n * Returns the width of a given element.\n * \n * @param node the element\n * @param client whether to use `clientWidth` if possible\n */\n\nexport default function getWidth(node, client) {\n  var win = getWindow(node);\n  return win ? win.innerWidth : client ? node.clientWidth : offset(node).width;\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAQe,SAAS,SAAS,IAAI,EAAE,MAAM;IAC3C,IAAI,MAAM,CAAA,GAAA,oJAAA,CAAA,UAAS,AAAD,EAAE;IACpB,OAAO,MAAM,IAAI,UAAU,GAAG,SAAS,KAAK,WAAW,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAM,AAAD,EAAE,MAAM,KAAK;AAC9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/scrollbarSize.js"], "sourcesContent": ["import canUseDOM from './canUseDOM';\nvar size;\nexport default function scrollbarSize(recalc) {\n  if (!size && size !== 0 || recalc) {\n    if (canUseDOM) {\n      var scrollDiv = document.createElement('div');\n      scrollDiv.style.position = 'absolute';\n      scrollDiv.style.top = '-9999px';\n      scrollDiv.style.width = '50px';\n      scrollDiv.style.height = '50px';\n      scrollDiv.style.overflow = 'scroll';\n      document.body.appendChild(scrollDiv);\n      size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      document.body.removeChild(scrollDiv);\n    }\n  }\n\n  return size;\n}"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI;AACW,SAAS,cAAc,MAAM;IAC1C,IAAI,CAAC,QAAQ,SAAS,KAAK,QAAQ;QACjC,IAAI,qJAAA,CAAA,UAAS,EAAE;YACb,IAAI,YAAY,SAAS,aAAa,CAAC;YACvC,UAAU,KAAK,CAAC,QAAQ,GAAG;YAC3B,UAAU,KAAK,CAAC,GAAG,GAAG;YACtB,UAAU,KAAK,CAAC,KAAK,GAAG;YACxB,UAAU,KAAK,CAAC,MAAM,GAAG;YACzB,UAAU,KAAK,CAAC,QAAQ,GAAG;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,UAAU,WAAW,GAAG,UAAU,WAAW;YACpD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2781, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/hasClass.js"], "sourcesContent": ["/**\n * Checks if a given element has a CSS class.\n * \n * @param element the element\n * @param className the CSS class name\n */\nexport default function hasClass(element, className) {\n  if (element.classList) return !!className && element.classList.contains(className);\n  return (\" \" + (element.className.baseVal || element.className) + \" \").indexOf(\" \" + className + \" \") !== -1;\n}"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACc,SAAS,SAAS,OAAO,EAAE,SAAS;IACjD,IAAI,QAAQ,SAAS,EAAE,OAAO,CAAC,CAAC,aAAa,QAAQ,SAAS,CAAC,QAAQ,CAAC;IACxE,OAAO,CAAC,MAAM,CAAC,QAAQ,SAAS,CAAC,OAAO,IAAI,QAAQ,SAAS,IAAI,GAAG,EAAE,OAAO,CAAC,MAAM,YAAY,SAAS,CAAC;AAC5G", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2799, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/addClass.js"], "sourcesContent": ["import hasClass from './hasClass';\n/**\n * Adds a CSS class to a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\nexport default function addClass(element, className) {\n  if (element.classList) element.classList.add(className);else if (!hasClass(element, className)) if (typeof element.className === 'string') element.className = element.className + \" \" + className;else element.setAttribute('class', (element.className && element.className.baseVal || '') + \" \" + className);\n}"], "names": [], "mappings": ";;;AAAA;;AAQe,SAAS,SAAS,OAAO,EAAE,SAAS;IACjD,IAAI,QAAQ,SAAS,EAAE,QAAQ,SAAS,CAAC,GAAG,CAAC;SAAgB,IAAI,CAAC,CAAA,GAAA,oJAAA,CAAA,UAAQ,AAAD,EAAE,SAAS,YAAY,IAAI,OAAO,QAAQ,SAAS,KAAK,UAAU,QAAQ,SAAS,GAAG,QAAQ,SAAS,GAAG,MAAM;SAAe,QAAQ,YAAY,CAAC,SAAS,CAAC,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,OAAO,IAAI,EAAE,IAAI,MAAM;AACvS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2815, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dom-helpers/esm/removeClass.js"], "sourcesContent": ["function replaceClassName(origClass, classToRemove) {\n  return origClass.replace(new RegExp(\"(^|\\\\s)\" + classToRemove + \"(?:\\\\s|$)\", 'g'), '$1').replace(/\\s+/g, ' ').replace(/^\\s*|\\s*$/g, '');\n}\n/**\n * Removes a CSS class from a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\n\nexport default function removeClass(element, className) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else if (typeof element.className === 'string') {\n    element.className = replaceClassName(element.className, className);\n  } else {\n    element.setAttribute('class', replaceClassName(element.className && element.className.baseVal || '', className));\n  }\n}"], "names": [], "mappings": ";;;AAAA,SAAS,iBAAiB,SAAS,EAAE,aAAa;IAChD,OAAO,UAAU,OAAO,CAAC,IAAI,OAAO,YAAY,gBAAgB,aAAa,MAAM,MAAM,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,cAAc;AACtI;AASe,SAAS,YAAY,OAAO,EAAE,SAAS;IACpD,IAAI,QAAQ,SAAS,EAAE;QACrB,QAAQ,SAAS,CAAC,MAAM,CAAC;IAC3B,OAAO,IAAI,OAAO,QAAQ,SAAS,KAAK,UAAU;QAChD,QAAQ,SAAS,GAAG,iBAAiB,QAAQ,SAAS,EAAE;IAC1D,OAAO;QACL,QAAQ,YAAY,CAAC,SAAS,iBAAiB,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,OAAO,IAAI,IAAI;IACvG;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40restart/hooks/esm/useCallbackRef.js"], "sourcesContent": ["import { useState } from 'react';\n\n/**\n * A convenience hook around `useState` designed to be paired with\n * the component [callback ref](https://reactjs.org/docs/refs-and-the-dom.html#callback-refs) api.\n * Callback refs are useful over `useRef()` when you need to respond to the ref being set\n * instead of lazily accessing it in an effect.\n *\n * ```ts\n * const [element, attachRef] = useCallbackRef<HTMLDivElement>()\n *\n * useEffect(() => {\n *   if (!element) return\n *\n *   const calendar = new FullCalendar.Calendar(element)\n *\n *   return () => {\n *     calendar.destroy()\n *   }\n * }, [element])\n *\n * return <div ref={attachRef} />\n * ```\n *\n * @category refs\n */\nexport default function useCallbackRef() {\n  return useState(null);\n}"], "names": [], "mappings": ";;;AAAA;;AA0Be,SAAS;IACtB,OAAO,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2850, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40restart/hooks/esm/useMergedRefs.js"], "sourcesContent": ["import { useMemo } from 'react';\nconst toFnRef = ref => !ref || typeof ref === 'function' ? ref : value => {\n  ref.current = value;\n};\nexport function mergeRefs(refA, refB) {\n  const a = toFnRef(refA);\n  const b = toFnRef(refB);\n  return value => {\n    if (a) a(value);\n    if (b) b(value);\n  };\n}\n\n/**\n * Create and returns a single callback ref composed from two other Refs.\n *\n * ```tsx\n * const Button = React.forwardRef((props, ref) => {\n *   const [element, attachRef] = useCallbackRef<HTMLButtonElement>();\n *   const mergedRef = useMergedRefs(ref, attachRef);\n *\n *   return <button ref={mergedRef} {...props}/>\n * })\n * ```\n *\n * @param refA A Callback or mutable Ref\n * @param refB A Callback or mutable Ref\n * @category refs\n */\nfunction useMergedRefs(refA, refB) {\n  return useMemo(() => mergeRefs(refA, refB), [refA, refB]);\n}\nexport default useMergedRefs;"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,UAAU,CAAA,MAAO,CAAC,OAAO,OAAO,QAAQ,aAAa,MAAM,CAAA;QAC/D,IAAI,OAAO,GAAG;IAChB;AACO,SAAS,UAAU,IAAI,EAAE,IAAI;IAClC,MAAM,IAAI,QAAQ;IAClB,MAAM,IAAI,QAAQ;IAClB,OAAO,CAAA;QACL,IAAI,GAAG,EAAE;QACT,IAAI,GAAG,EAAE;IACX;AACF;AAEA;;;;;;;;;;;;;;;CAeC,GACD,SAAS,cAAc,IAAI,EAAE,IAAI;IAC/B,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iCAAE,IAAM,UAAU,MAAM;gCAAO;QAAC;QAAM;KAAK;AAC1D;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40restart/hooks/esm/useMounted.js"], "sourcesContent": ["import { useRef, useEffect } from 'react';\n\n/**\n * Track whether a component is current mounted. Generally less preferable than\n * properlly canceling effects so they don't run after a component is unmounted,\n * but helpful in cases where that isn't feasible, such as a `Promise` resolution.\n *\n * @returns a function that returns the current isMounted state of the component\n *\n * ```ts\n * const [data, setData] = useState(null)\n * const isMounted = useMounted()\n *\n * useEffect(() => {\n *   fetchdata().then((newData) => {\n *      if (isMounted()) {\n *        setData(newData);\n *      }\n *   })\n * })\n * ```\n */\nexport default function useMounted() {\n  const mounted = useRef(true);\n  const isMounted = useRef(() => mounted.current);\n  useEffect(() => {\n    mounted.current = true;\n    return () => {\n      mounted.current = false;\n    };\n  }, []);\n  return isMounted.current;\n}"], "names": [], "mappings": ";;;AAAA;;AAsBe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;wCAAE,IAAM,QAAQ,OAAO;;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,QAAQ,OAAO,GAAG;YAClB;wCAAO;oBACL,QAAQ,OAAO,GAAG;gBACpB;;QACF;+BAAG,EAAE;IACL,OAAO,UAAU,OAAO;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2925, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40restart/hooks/esm/useSafeState.js"], "sourcesContent": ["import { useCallback } from 'react';\nimport useMounted from './useMounted';\n\n/**\n * `useSafeState` takes the return value of a `useState` hook and wraps the\n * setter to prevent updates onces the component has unmounted. Can used\n * with `useMergeState` and `useStateAsync` as well\n *\n * @param state The return value of a useStateHook\n *\n * ```ts\n * const [show, setShow] = useSafeState(useState(true));\n * ```\n */\n\nfunction useSafeState(state) {\n  const isMounted = useMounted();\n  return [state[0], useCallback(nextState => {\n    if (!isMounted()) return;\n    return state[1](nextState);\n  }, [isMounted, state[1]])];\n}\nexport default useSafeState;"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;;;;;;;;;CAUC,GAED,SAAS,aAAa,KAAK;IACzB,MAAM,YAAY,CAAA,GAAA,0JAAA,CAAA,UAAU,AAAD;IAC3B,OAAO;QAAC,KAAK,CAAC,EAAE;QAAE,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wCAAE,CAAA;gBAC5B,IAAI,CAAC,aAAa;gBAClB,OAAO,KAAK,CAAC,EAAE,CAAC;YAClB;uCAAG;YAAC;YAAW,KAAK,CAAC,EAAE;SAAC;KAAE;AAC5B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40restart/hooks/esm/useCommittedRef.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\n\n/**\n * Creates a `Ref` whose value is updated in an effect, ensuring the most recent\n * value is the one rendered with. Generally only required for Concurrent mode usage\n * where previous work in `render()` may be discarded before being used.\n *\n * This is safe to access in an event handler.\n *\n * @param value The `Ref` value\n */\nfunction useCommittedRef(value) {\n  const ref = useRef(value);\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n  return ref;\n}\nexport default useCommittedRef;"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;;;;;CAQC,GACD,SAAS,gBAAgB,KAAK;IAC5B,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,OAAO,GAAG;QAChB;oCAAG;QAAC;KAAM;IACV,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/%40restart/hooks/esm/useEventCallback.js"], "sourcesContent": ["import { useCallback } from 'react';\nimport useCommittedRef from './useCommittedRef';\nexport default function useEventCallback(fn) {\n  const ref = useCommittedRef(fn);\n  return useCallback(function (...args) {\n    return ref.current && ref.current(...args);\n  }, [ref]);\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AACe,SAAS,iBAAiB,EAAE;IACzC,MAAM,MAAM,CAAA,GAAA,+JAAA,CAAA,UAAe,AAAD,EAAE;IAC5B,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wCAAE,SAAU,GAAG,IAAI;YAClC,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI;QACvC;uCAAG;QAAC;KAAI;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-overlays/esm/popper.js"], "sourcesContent": ["import arrow from '@popperjs/core/lib/modifiers/arrow';\nimport computeStyles from '@popperjs/core/lib/modifiers/computeStyles';\nimport eventListeners from '@popperjs/core/lib/modifiers/eventListeners';\nimport flip from '@popperjs/core/lib/modifiers/flip';\nimport hide from '@popperjs/core/lib/modifiers/hide';\nimport offset from '@popperjs/core/lib/modifiers/offset';\nimport popperOffsets from '@popperjs/core/lib/modifiers/popperOffsets';\nimport preventOverflow from '@popperjs/core/lib/modifiers/preventOverflow';\nimport { placements } from '@popperjs/core/lib/enums';\nimport { popperGenerator } from '@popperjs/core/lib/popper-base'; // For the common JS build we will turn this file into a bundle with no imports.\n// This is b/c the Popper lib is all esm files, and would break in a common js only environment\n\nexport var createPopper = popperGenerator({\n  defaultModifiers: [hide, popperOffsets, computeStyles, eventListeners, offset, flip, preventOverflow, arrow]\n});\nexport { placements };"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,4SAAkE,gFAAgF;;;;;;;;;;;AAG3I,IAAI,eAAe,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;IACxC,kBAAkB;QAAC,iKAAA,CAAA,UAAI;QAAE,0KAAA,CAAA,UAAa;QAAE,0KAAA,CAAA,UAAa;QAAE,2KAAA,CAAA,UAAc;QAAE,mKAAA,CAAA,UAAM;QAAE,iKAAA,CAAA,UAAI;QAAE,4KAAA,CAAA,UAAe;QAAE,kKAAA,CAAA,UAAK;KAAC;AAC9G", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3059, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-overlays/esm/usePopper.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport useSafeState from '@restart/hooks/useSafeState';\nimport { createPopper } from './popper';\n\nvar initialPopperStyles = function initialPopperStyles(position) {\n  return {\n    position: position,\n    top: '0',\n    left: '0',\n    opacity: '0',\n    pointerEvents: 'none'\n  };\n};\n\nvar disabledApplyStylesModifier = {\n  name: 'applyStyles',\n  enabled: false\n}; // In order to satisfy the current usage of options, including undefined\n\nvar ariaDescribedByModifier = {\n  name: 'ariaDescribedBy',\n  enabled: true,\n  phase: 'afterWrite',\n  effect: function effect(_ref) {\n    var state = _ref.state;\n    return function () {\n      var _state$elements = state.elements,\n          reference = _state$elements.reference,\n          popper = _state$elements.popper;\n\n      if ('removeAttribute' in reference) {\n        var ids = (reference.getAttribute('aria-describedby') || '').split(',').filter(function (id) {\n          return id.trim() !== popper.id;\n        });\n        if (!ids.length) reference.removeAttribute('aria-describedby');else reference.setAttribute('aria-describedby', ids.join(','));\n      }\n    };\n  },\n  fn: function fn(_ref2) {\n    var _popper$getAttribute;\n\n    var state = _ref2.state;\n    var _state$elements2 = state.elements,\n        popper = _state$elements2.popper,\n        reference = _state$elements2.reference;\n    var role = (_popper$getAttribute = popper.getAttribute('role')) == null ? void 0 : _popper$getAttribute.toLowerCase();\n\n    if (popper.id && role === 'tooltip' && 'setAttribute' in reference) {\n      var ids = reference.getAttribute('aria-describedby');\n\n      if (ids && ids.split(',').indexOf(popper.id) !== -1) {\n        return;\n      }\n\n      reference.setAttribute('aria-describedby', ids ? ids + \",\" + popper.id : popper.id);\n    }\n  }\n};\nvar EMPTY_MODIFIERS = [];\n/**\n * Position an element relative some reference element using Popper.js\n *\n * @param referenceElement\n * @param popperElement\n * @param {object}      options\n * @param {object=}     options.modifiers Popper.js modifiers\n * @param {boolean=}    options.enabled toggle the popper functionality on/off\n * @param {string=}     options.placement The popper element placement relative to the reference element\n * @param {string=}     options.strategy the positioning strategy\n * @param {boolean=}    options.eventsEnabled have Popper listen on window resize events to reposition the element\n * @param {function=}   options.onCreate called when the popper is created\n * @param {function=}   options.onUpdate called when the popper is updated\n *\n * @returns {UsePopperState} The popper state\n */\n\nfunction usePopper(referenceElement, popperElement, _temp) {\n  var _ref3 = _temp === void 0 ? {} : _temp,\n      _ref3$enabled = _ref3.enabled,\n      enabled = _ref3$enabled === void 0 ? true : _ref3$enabled,\n      _ref3$placement = _ref3.placement,\n      placement = _ref3$placement === void 0 ? 'bottom' : _ref3$placement,\n      _ref3$strategy = _ref3.strategy,\n      strategy = _ref3$strategy === void 0 ? 'absolute' : _ref3$strategy,\n      _ref3$modifiers = _ref3.modifiers,\n      modifiers = _ref3$modifiers === void 0 ? EMPTY_MODIFIERS : _ref3$modifiers,\n      config = _objectWithoutPropertiesLoose(_ref3, [\"enabled\", \"placement\", \"strategy\", \"modifiers\"]);\n\n  var popperInstanceRef = useRef();\n  var update = useCallback(function () {\n    var _popperInstanceRef$cu;\n\n    (_popperInstanceRef$cu = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu.update();\n  }, []);\n  var forceUpdate = useCallback(function () {\n    var _popperInstanceRef$cu2;\n\n    (_popperInstanceRef$cu2 = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu2.forceUpdate();\n  }, []);\n\n  var _useSafeState = useSafeState(useState({\n    placement: placement,\n    update: update,\n    forceUpdate: forceUpdate,\n    attributes: {},\n    styles: {\n      popper: initialPopperStyles(strategy),\n      arrow: {}\n    }\n  })),\n      popperState = _useSafeState[0],\n      setState = _useSafeState[1];\n\n  var updateModifier = useMemo(function () {\n    return {\n      name: 'updateStateModifier',\n      enabled: true,\n      phase: 'write',\n      requires: ['computeStyles'],\n      fn: function fn(_ref4) {\n        var state = _ref4.state;\n        var styles = {};\n        var attributes = {};\n        Object.keys(state.elements).forEach(function (element) {\n          styles[element] = state.styles[element];\n          attributes[element] = state.attributes[element];\n        });\n        setState({\n          state: state,\n          styles: styles,\n          attributes: attributes,\n          update: update,\n          forceUpdate: forceUpdate,\n          placement: state.placement\n        });\n      }\n    };\n  }, [update, forceUpdate, setState]);\n  useEffect(function () {\n    if (!popperInstanceRef.current || !enabled) return;\n    popperInstanceRef.current.setOptions({\n      placement: placement,\n      strategy: strategy,\n      modifiers: [].concat(modifiers, [updateModifier, disabledApplyStylesModifier])\n    }); // intentionally NOT re-running on new modifiers\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [strategy, placement, updateModifier, enabled]);\n  useEffect(function () {\n    if (!enabled || referenceElement == null || popperElement == null) {\n      return undefined;\n    }\n\n    popperInstanceRef.current = createPopper(referenceElement, popperElement, _extends({}, config, {\n      placement: placement,\n      strategy: strategy,\n      modifiers: [].concat(modifiers, [ariaDescribedByModifier, updateModifier])\n    }));\n    return function () {\n      if (popperInstanceRef.current != null) {\n        popperInstanceRef.current.destroy();\n        popperInstanceRef.current = undefined;\n        setState(function (s) {\n          return _extends({}, s, {\n            attributes: {},\n            styles: {\n              popper: initialPopperStyles(strategy)\n            }\n          });\n        });\n      }\n    }; // This is only run once to _create_ the popper\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [enabled, referenceElement, popperElement]);\n  return popperState;\n}\n\nexport default usePopper;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,IAAI,sBAAsB,SAAS,oBAAoB,QAAQ;IAC7D,OAAO;QACL,UAAU;QACV,KAAK;QACL,MAAM;QACN,SAAS;QACT,eAAe;IACjB;AACF;AAEA,IAAI,8BAA8B;IAChC,MAAM;IACN,SAAS;AACX,GAAG,wEAAwE;AAE3E,IAAI,0BAA0B;IAC5B,MAAM;IACN,SAAS;IACT,OAAO;IACP,QAAQ,SAAS,OAAO,IAAI;QAC1B,IAAI,QAAQ,KAAK,KAAK;QACtB,OAAO;YACL,IAAI,kBAAkB,MAAM,QAAQ,EAChC,YAAY,gBAAgB,SAAS,EACrC,SAAS,gBAAgB,MAAM;YAEnC,IAAI,qBAAqB,WAAW;gBAClC,IAAI,MAAM,CAAC,UAAU,YAAY,CAAC,uBAAuB,EAAE,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,SAAU,EAAE;oBACzF,OAAO,GAAG,IAAI,OAAO,OAAO,EAAE;gBAChC;gBACA,IAAI,CAAC,IAAI,MAAM,EAAE,UAAU,eAAe,CAAC;qBAAyB,UAAU,YAAY,CAAC,oBAAoB,IAAI,IAAI,CAAC;YAC1H;QACF;IACF;IACA,IAAI,SAAS,GAAG,KAAK;QACnB,IAAI;QAEJ,IAAI,QAAQ,MAAM,KAAK;QACvB,IAAI,mBAAmB,MAAM,QAAQ,EACjC,SAAS,iBAAiB,MAAM,EAChC,YAAY,iBAAiB,SAAS;QAC1C,IAAI,OAAO,CAAC,uBAAuB,OAAO,YAAY,CAAC,OAAO,KAAK,OAAO,KAAK,IAAI,qBAAqB,WAAW;QAEnH,IAAI,OAAO,EAAE,IAAI,SAAS,aAAa,kBAAkB,WAAW;YAClE,IAAI,MAAM,UAAU,YAAY,CAAC;YAEjC,IAAI,OAAO,IAAI,KAAK,CAAC,KAAK,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG;gBACnD;YACF;YAEA,UAAU,YAAY,CAAC,oBAAoB,MAAM,MAAM,MAAM,OAAO,EAAE,GAAG,OAAO,EAAE;QACpF;IACF;AACF;AACA,IAAI,kBAAkB,EAAE;AACxB;;;;;;;;;;;;;;;CAeC,GAED,SAAS,UAAU,gBAAgB,EAAE,aAAa,EAAE,KAAK;IACvD,IAAI,QAAQ,UAAU,KAAK,IAAI,CAAC,IAAI,OAChC,gBAAgB,MAAM,OAAO,EAC7B,UAAU,kBAAkB,KAAK,IAAI,OAAO,eAC5C,kBAAkB,MAAM,SAAS,EACjC,YAAY,oBAAoB,KAAK,IAAI,WAAW,iBACpD,iBAAiB,MAAM,QAAQ,EAC/B,WAAW,mBAAmB,KAAK,IAAI,aAAa,gBACpD,kBAAkB,MAAM,SAAS,EACjC,YAAY,oBAAoB,KAAK,IAAI,kBAAkB,iBAC3D,SAAS,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;QAAC;QAAW;QAAa;QAAY;KAAY;IAEnG,IAAI,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC7B,IAAI,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE;YACvB,IAAI;YAEJ,CAAC,wBAAwB,kBAAkB,OAAO,KAAK,OAAO,KAAK,IAAI,sBAAsB,MAAM;QACrG;wCAAG,EAAE;IACL,IAAI,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC5B,IAAI;YAEJ,CAAC,yBAAyB,kBAAkB,OAAO,KAAK,OAAO,KAAK,IAAI,uBAAuB,WAAW;QAC5G;6CAAG,EAAE;IAEL,IAAI,gBAAgB,CAAA,GAAA,4JAAA,CAAA,UAAY,AAAD,EAAE,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACxC,WAAW;QACX,QAAQ;QACR,aAAa;QACb,YAAY,CAAC;QACb,QAAQ;YACN,QAAQ,oBAAoB;YAC5B,OAAO,CAAC;QACV;IACF,KACI,cAAc,aAAa,CAAC,EAAE,EAC9B,WAAW,aAAa,CAAC,EAAE;IAE/B,IAAI,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE;YAC3B,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,UAAU;oBAAC;iBAAgB;gBAC3B,IAAI,SAAS,GAAG,KAAK;oBACnB,IAAI,QAAQ,MAAM,KAAK;oBACvB,IAAI,SAAS,CAAC;oBACd,IAAI,aAAa,CAAC;oBAClB,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAE,OAAO;gEAAC,SAAU,OAAO;4BACnD,MAAM,CAAC,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ;4BACvC,UAAU,CAAC,QAAQ,GAAG,MAAM,UAAU,CAAC,QAAQ;wBACjD;;oBACA,SAAS;wBACP,OAAO;wBACP,QAAQ;wBACR,YAAY;wBACZ,QAAQ;wBACR,aAAa;wBACb,WAAW,MAAM,SAAS;oBAC5B;gBACF;YACF;QACF;4CAAG;QAAC;QAAQ;QAAa;KAAS;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,SAAS;YAC5C,kBAAkB,OAAO,CAAC,UAAU,CAAC;gBACnC,WAAW;gBACX,UAAU;gBACV,WAAW,EAAE,CAAC,MAAM,CAAC,WAAW;oBAAC;oBAAgB;iBAA4B;YAC/E,IAAI,gDAAgD;QACpD,uDAAuD;QACzD;8BAAG;QAAC;QAAU;QAAW;QAAgB;KAAQ;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,WAAW,oBAAoB,QAAQ,iBAAiB,MAAM;gBACjE,OAAO;YACT;YAEA,kBAAkB,OAAO,GAAG,CAAA,GAAA,qKAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,QAAQ;gBAC7F,WAAW;gBACX,UAAU;gBACV,WAAW,EAAE,CAAC,MAAM,CAAC,WAAW;oBAAC;oBAAyB;iBAAe;YAC3E;YACA;uCAAO;oBACL,IAAI,kBAAkB,OAAO,IAAI,MAAM;wBACrC,kBAAkB,OAAO,CAAC,OAAO;wBACjC,kBAAkB,OAAO,GAAG;wBAC5B;mDAAS,SAAU,CAAC;gCAClB,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,GAAG;oCACrB,YAAY,CAAC;oCACb,QAAQ;wCACN,QAAQ,oBAAoB;oCAC9B;gCACF;4BACF;;oBACF;gBACF;uCAAG,+CAA+C;QAClD,uDAAuD;QACzD;8BAAG;QAAC;QAAS;QAAkB;KAAc;IAC7C,OAAO;AACT;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-overlays/esm/safeFindDOMNode.js"], "sourcesContent": ["import ReactDOM from 'react-dom';\nexport default function safeFindDOMNode(componentOrElement) {\n  if (componentOrElement && 'setState' in componentOrElement) {\n    return ReactDOM.findDOMNode(componentOrElement);\n  }\n\n  return componentOrElement != null ? componentOrElement : null;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,gBAAgB,kBAAkB;IACxD,IAAI,sBAAsB,cAAc,oBAAoB;QAC1D,OAAO,oKAAA,CAAA,UAAQ,CAAC,WAAW,CAAC;IAC9B;IAEA,OAAO,sBAAsB,OAAO,qBAAqB;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-overlays/esm/ownerDocument.js"], "sourcesContent": ["import ownerDocument from 'dom-helpers/ownerDocument';\nimport safeFindDOMNode from './safeFindDOMNode';\nexport default (function (componentOrElement) {\n  return ownerDocument(safeFindDOMNode(componentOrElement));\n});"], "names": [], "mappings": ";;;AAAA;AACA;;;uCACgB,SAAU,kBAAkB;IAC1C,OAAO,CAAA,GAAA,yJAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,UAAe,AAAD,EAAE;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-overlays/esm/useRootClose.js"], "sourcesContent": ["import contains from 'dom-helpers/contains';\nimport listen from 'dom-helpers/listen';\nimport { useCallback, useEffect, useRef } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport warning from 'warning';\nimport ownerDocument from './ownerDocument';\nvar escapeKeyCode = 27;\n\nvar noop = function noop() {};\n\nfunction isLeftClickEvent(event) {\n  return event.button === 0;\n}\n\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nvar getRefTarget = function getRefTarget(ref) {\n  return ref && ('current' in ref ? ref.current : ref);\n};\n\n/**\n * The `useRootClose` hook registers your callback on the document\n * when rendered. Powers the `<Overlay/>` component. This is used achieve modal\n * style behavior where your callback is triggered when the user tries to\n * interact with the rest of the document or hits the `esc` key.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onRootClose\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useRootClose(ref, onRootClose, _temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      disabled = _ref.disabled,\n      _ref$clickTrigger = _ref.clickTrigger,\n      clickTrigger = _ref$clickTrigger === void 0 ? 'click' : _ref$clickTrigger;\n\n  var preventMouseRootCloseRef = useRef(false);\n  var onClose = onRootClose || noop;\n  var handleMouseCapture = useCallback(function (e) {\n    var _e$composedPath$;\n\n    var currentTarget = getRefTarget(ref);\n    warning(!!currentTarget, 'RootClose captured a close event but does not have a ref to compare it to. ' + 'useRootClose(), should be passed a ref that resolves to a DOM node');\n    preventMouseRootCloseRef.current = !currentTarget || isModifiedEvent(e) || !isLeftClickEvent(e) || !!contains(currentTarget, (_e$composedPath$ = e.composedPath == null ? void 0 : e.composedPath()[0]) != null ? _e$composedPath$ : e.target);\n  }, [ref]);\n  var handleMouse = useEventCallback(function (e) {\n    if (!preventMouseRootCloseRef.current) {\n      onClose(e);\n    }\n  });\n  var handleKeyUp = useEventCallback(function (e) {\n    if (e.keyCode === escapeKeyCode) {\n      onClose(e);\n    }\n  });\n  useEffect(function () {\n    if (disabled || ref == null) return undefined; // Store the current event to avoid triggering handlers immediately\n    // https://github.com/facebook/react/issues/20074\n\n    var currentEvent = window.event;\n    var doc = ownerDocument(getRefTarget(ref)); // Use capture for this listener so it fires before React's listener, to\n    // avoid false positives in the contains() check below if the target DOM\n    // element is removed in the React mouse callback.\n\n    var removeMouseCaptureListener = listen(doc, clickTrigger, handleMouseCapture, true);\n    var removeMouseListener = listen(doc, clickTrigger, function (e) {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n\n      handleMouse(e);\n    });\n    var removeKeyupListener = listen(doc, 'keyup', function (e) {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n\n      handleKeyUp(e);\n    });\n    var mobileSafariHackListeners = [];\n\n    if ('ontouchstart' in doc.documentElement) {\n      mobileSafariHackListeners = [].slice.call(doc.body.children).map(function (el) {\n        return listen(el, 'mousemove', noop);\n      });\n    }\n\n    return function () {\n      removeMouseCaptureListener();\n      removeMouseListener();\n      removeKeyupListener();\n      mobileSafariHackListeners.forEach(function (remove) {\n        return remove();\n      });\n    };\n  }, [ref, disabled, clickTrigger, handleMouseCapture, handleMouse, handleKeyUp]);\n}\n\nexport default useRootClose;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,gBAAgB;AAEpB,IAAI,OAAO,SAAS,QAAQ;AAE5B,SAAS,iBAAiB,KAAK;IAC7B,OAAO,MAAM,MAAM,KAAK;AAC1B;AAEA,SAAS,gBAAgB,KAAK;IAC5B,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,IAAI,MAAM,QAAQ;AAC5E;AAEA,IAAI,eAAe,SAAS,aAAa,GAAG;IAC1C,OAAO,OAAO,CAAC,aAAa,MAAM,IAAI,OAAO,GAAG,GAAG;AACrD;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,aAAa,GAAG,EAAE,WAAW,EAAE,KAAK;IAC3C,IAAI,OAAO,UAAU,KAAK,IAAI,CAAC,IAAI,OAC/B,WAAW,KAAK,QAAQ,EACxB,oBAAoB,KAAK,YAAY,EACrC,eAAe,sBAAsB,KAAK,IAAI,UAAU;IAE5D,IAAI,2BAA2B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtC,IAAI,UAAU,eAAe;IAC7B,IAAI,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,SAAU,CAAC;YAC9C,IAAI;YAEJ,IAAI,gBAAgB,aAAa;YACjC,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,CAAC,CAAC,eAAe,gFAAgF;YACzG,yBAAyB,OAAO,GAAG,CAAC,iBAAiB,gBAAgB,MAAM,CAAC,iBAAiB,MAAM,CAAC,CAAC,CAAA,GAAA,oJAAA,CAAA,UAAQ,AAAD,EAAE,eAAe,CAAC,mBAAmB,EAAE,YAAY,IAAI,OAAO,KAAK,IAAI,EAAE,YAAY,EAAE,CAAC,EAAE,KAAK,OAAO,mBAAmB,EAAE,MAAM;QAC/O;uDAAG;QAAC;KAAI;IACR,IAAI,cAAc,CAAA,GAAA,gKAAA,CAAA,UAAgB,AAAD;sDAAE,SAAU,CAAC;YAC5C,IAAI,CAAC,yBAAyB,OAAO,EAAE;gBACrC,QAAQ;YACV;QACF;;IACA,IAAI,cAAc,CAAA,GAAA,gKAAA,CAAA,UAAgB,AAAD;sDAAE,SAAU,CAAC;YAC5C,IAAI,EAAE,OAAO,KAAK,eAAe;gBAC/B,QAAQ;YACV;QACF;;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,YAAY,OAAO,MAAM,OAAO,WAAW,mEAAmE;YAClH,iDAAiD;YAEjD,IAAI,eAAe,OAAO,KAAK;YAC/B,IAAI,MAAM,CAAA,GAAA,4JAAA,CAAA,UAAa,AAAD,EAAE,aAAa,OAAO,wEAAwE;YACpH,wEAAwE;YACxE,kDAAkD;YAElD,IAAI,6BAA6B,CAAA,GAAA,kJAAA,CAAA,UAAM,AAAD,EAAE,KAAK,cAAc,oBAAoB;YAC/E,IAAI,sBAAsB,CAAA,GAAA,kJAAA,CAAA,UAAM,AAAD,EAAE,KAAK;8DAAc,SAAU,CAAC;oBAC7D,+EAA+E;oBAC/E,IAAI,MAAM,cAAc;wBACtB,eAAe;wBACf;oBACF;oBAEA,YAAY;gBACd;;YACA,IAAI,sBAAsB,CAAA,GAAA,kJAAA,CAAA,UAAM,AAAD,EAAE,KAAK;8DAAS,SAAU,CAAC;oBACxD,+EAA+E;oBAC/E,IAAI,MAAM,cAAc;wBACtB,eAAe;wBACf;oBACF;oBAEA,YAAY;gBACd;;YACA,IAAI,4BAA4B,EAAE;YAElC,IAAI,kBAAkB,IAAI,eAAe,EAAE;gBACzC,4BAA4B,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,GAAG;8CAAC,SAAU,EAAE;wBAC3E,OAAO,CAAA,GAAA,kJAAA,CAAA,UAAM,AAAD,EAAE,IAAI,aAAa;oBACjC;;YACF;YAEA;0CAAO;oBACL;oBACA;oBACA;oBACA,0BAA0B,OAAO;kDAAC,SAAU,MAAM;4BAChD,OAAO;wBACT;;gBACF;;QACF;iCAAG;QAAC;QAAK;QAAU;QAAc;QAAoB;QAAa;KAAY;AAChF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-overlays/esm/useWaitForDOMRef.js"], "sourcesContent": ["import ownerDocument from 'dom-helpers/ownerDocument';\nimport { useState, useEffect } from 'react';\nexport var resolveContainerRef = function resolveContainerRef(ref) {\n  var _ref;\n\n  if (typeof document === 'undefined') return null;\n  if (ref == null) return ownerDocument().body;\n  if (typeof ref === 'function') ref = ref();\n  if (ref && 'current' in ref) ref = ref.current;\n  if ((_ref = ref) != null && _ref.nodeType) return ref || null;\n  return null;\n};\nexport default function useWaitForDOMRef(ref, onResolved) {\n  var _useState = useState(function () {\n    return resolveContainerRef(ref);\n  }),\n      resolvedRef = _useState[0],\n      setRef = _useState[1];\n\n  if (!resolvedRef) {\n    var earlyRef = resolveContainerRef(ref);\n    if (earlyRef) setRef(earlyRef);\n  }\n\n  useEffect(function () {\n    if (onResolved && resolvedRef) {\n      onResolved(resolvedRef);\n    }\n  }, [onResolved, resolvedRef]);\n  useEffect(function () {\n    var nextRef = resolveContainerRef(ref);\n\n    if (nextRef !== resolvedRef) {\n      setRef(nextRef);\n    }\n  }, [ref, resolvedRef]);\n  return resolvedRef;\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,IAAI,sBAAsB,SAAS,oBAAoB,GAAG;IAC/D,IAAI;IAEJ,IAAI,OAAO,aAAa,aAAa,OAAO;IAC5C,IAAI,OAAO,MAAM,OAAO,CAAA,GAAA,yJAAA,CAAA,UAAa,AAAD,IAAI,IAAI;IAC5C,IAAI,OAAO,QAAQ,YAAY,MAAM;IACrC,IAAI,OAAO,aAAa,KAAK,MAAM,IAAI,OAAO;IAC9C,IAAI,CAAC,OAAO,GAAG,KAAK,QAAQ,KAAK,QAAQ,EAAE,OAAO,OAAO;IACzD,OAAO;AACT;AACe,SAAS,iBAAiB,GAAG,EAAE,UAAU;IACtD,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;gDAAE;YACvB,OAAO,oBAAoB;QAC7B;gDACI,cAAc,SAAS,CAAC,EAAE,EAC1B,SAAS,SAAS,CAAC,EAAE;IAEzB,IAAI,CAAC,aAAa;QAChB,IAAI,WAAW,oBAAoB;QACnC,IAAI,UAAU,OAAO;IACvB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,cAAc,aAAa;gBAC7B,WAAW;YACb;QACF;qCAAG;QAAC;QAAY;KAAY;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,UAAU,oBAAoB;YAElC,IAAI,YAAY,aAAa;gBAC3B,OAAO;YACT;QACF;qCAAG;QAAC;QAAK;KAAY;IACrB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-overlays/esm/mergeOptionsWithPopperConfig.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport function toModifierMap(modifiers) {\n  var result = {};\n\n  if (!Array.isArray(modifiers)) {\n    return modifiers || result;\n  } // eslint-disable-next-line no-unused-expressions\n\n\n  modifiers == null ? void 0 : modifiers.forEach(function (m) {\n    result[m.name] = m;\n  });\n  return result;\n}\nexport function toModifierArray(map) {\n  if (map === void 0) {\n    map = {};\n  }\n\n  if (Array.isArray(map)) return map;\n  return Object.keys(map).map(function (k) {\n    map[k].name = k;\n    return map[k];\n  });\n}\nexport default function mergeOptionsWithPopperConfig(_ref) {\n  var _modifiers$preventOve, _modifiers$preventOve2, _modifiers$offset, _modifiers$arrow;\n\n  var enabled = _ref.enabled,\n      enableEvents = _ref.enableEvents,\n      placement = _ref.placement,\n      flip = _ref.flip,\n      offset = _ref.offset,\n      fixed = _ref.fixed,\n      containerPadding = _ref.containerPadding,\n      arrowElement = _ref.arrowElement,\n      _ref$popperConfig = _ref.popperConfig,\n      popperConfig = _ref$popperConfig === void 0 ? {} : _ref$popperConfig;\n  var modifiers = toModifierMap(popperConfig.modifiers);\n  return _extends({}, popperConfig, {\n    placement: placement,\n    enabled: enabled,\n    strategy: fixed ? 'fixed' : popperConfig.strategy,\n    modifiers: toModifierArray(_extends({}, modifiers, {\n      eventListeners: {\n        enabled: enableEvents\n      },\n      preventOverflow: _extends({}, modifiers.preventOverflow, {\n        options: containerPadding ? _extends({\n          padding: containerPadding\n        }, (_modifiers$preventOve = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve.options) : (_modifiers$preventOve2 = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve2.options\n      }),\n      offset: {\n        options: _extends({\n          offset: offset\n        }, (_modifiers$offset = modifiers.offset) == null ? void 0 : _modifiers$offset.options)\n      },\n      arrow: _extends({}, modifiers.arrow, {\n        enabled: !!arrowElement,\n        options: _extends({}, (_modifiers$arrow = modifiers.arrow) == null ? void 0 : _modifiers$arrow.options, {\n          element: arrowElement\n        })\n      }),\n      flip: _extends({\n        enabled: !!flip\n      }, modifiers.flip)\n    }))\n  });\n}"], "names": [], "mappings": ";;;;;AAAA;;AACO,SAAS,cAAc,SAAS;IACrC,IAAI,SAAS,CAAC;IAEd,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY;QAC7B,OAAO,aAAa;IACtB,EAAE,iDAAiD;IAGnD,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO,CAAC,SAAU,CAAC;QACxD,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG;IACnB;IACA,OAAO;AACT;AACO,SAAS,gBAAgB,GAAG;IACjC,IAAI,QAAQ,KAAK,GAAG;QAClB,MAAM,CAAC;IACT;IAEA,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO;IAC/B,OAAO,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,SAAU,CAAC;QACrC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG;QACd,OAAO,GAAG,CAAC,EAAE;IACf;AACF;AACe,SAAS,6BAA6B,IAAI;IACvD,IAAI,uBAAuB,wBAAwB,mBAAmB;IAEtE,IAAI,UAAU,KAAK,OAAO,EACtB,eAAe,KAAK,YAAY,EAChC,YAAY,KAAK,SAAS,EAC1B,OAAO,KAAK,IAAI,EAChB,SAAS,KAAK,MAAM,EACpB,QAAQ,KAAK,KAAK,EAClB,mBAAmB,KAAK,gBAAgB,EACxC,eAAe,KAAK,YAAY,EAChC,oBAAoB,KAAK,YAAY,EACrC,eAAe,sBAAsB,KAAK,IAAI,CAAC,IAAI;IACvD,IAAI,YAAY,cAAc,aAAa,SAAS;IACpD,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,cAAc;QAChC,WAAW;QACX,SAAS;QACT,UAAU,QAAQ,UAAU,aAAa,QAAQ;QACjD,WAAW,gBAAgB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,WAAW;YACjD,gBAAgB;gBACd,SAAS;YACX;YACA,iBAAiB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,UAAU,eAAe,EAAE;gBACvD,SAAS,mBAAmB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBACnC,SAAS;gBACX,GAAG,CAAC,wBAAwB,UAAU,eAAe,KAAK,OAAO,KAAK,IAAI,sBAAsB,OAAO,IAAI,CAAC,yBAAyB,UAAU,eAAe,KAAK,OAAO,KAAK,IAAI,uBAAuB,OAAO;YACnN;YACA,QAAQ;gBACN,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBAChB,QAAQ;gBACV,GAAG,CAAC,oBAAoB,UAAU,MAAM,KAAK,OAAO,KAAK,IAAI,kBAAkB,OAAO;YACxF;YACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,UAAU,KAAK,EAAE;gBACnC,SAAS,CAAC,CAAC;gBACX,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,CAAC,mBAAmB,UAAU,KAAK,KAAK,OAAO,KAAK,IAAI,iBAAiB,OAAO,EAAE;oBACtG,SAAS;gBACX;YACF;YACA,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;gBACb,SAAS,CAAC,CAAC;YACb,GAAG,UAAU,IAAI;QACnB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/react-overlays/esm/Overlay.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport PropTypes from 'prop-types';\nimport React, { useState } from 'react';\nimport ReactDOM from 'react-dom';\nimport useCallbackRef from '@restart/hooks/useCallbackRef';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { placements } from './popper';\nimport usePopper from './usePopper';\nimport useRootClose from './useRootClose';\nimport useWaitForDOMRef from './useWaitForDOMRef';\nimport mergeOptionsWithPopperConfig from './mergeOptionsWithPopperConfig';\n\n/**\n * Built on top of `Popper.js`, the overlay component is\n * great for custom tooltip overlays.\n */\nvar Overlay = /*#__PURE__*/React.forwardRef(function (props, outerRef) {\n  var flip = props.flip,\n      offset = props.offset,\n      placement = props.placement,\n      _props$containerPaddi = props.containerPadding,\n      containerPadding = _props$containerPaddi === void 0 ? 5 : _props$containerPaddi,\n      _props$popperConfig = props.popperConfig,\n      popperConfig = _props$popperConfig === void 0 ? {} : _props$popperConfig,\n      Transition = props.transition;\n\n  var _useCallbackRef = useCallbackRef(),\n      rootElement = _useCallbackRef[0],\n      attachRef = _useCallbackRef[1];\n\n  var _useCallbackRef2 = useCallbackRef(),\n      arrowElement = _useCallbackRef2[0],\n      attachArrowRef = _useCallbackRef2[1];\n\n  var mergedRef = useMergedRefs(attachRef, outerRef);\n  var container = useWaitForDOMRef(props.container);\n  var target = useWaitForDOMRef(props.target);\n\n  var _useState = useState(!props.show),\n      exited = _useState[0],\n      setExited = _useState[1];\n\n  var _usePopper = usePopper(target, rootElement, mergeOptionsWithPopperConfig({\n    placement: placement,\n    enableEvents: !!props.show,\n    containerPadding: containerPadding || 5,\n    flip: flip,\n    offset: offset,\n    arrowElement: arrowElement,\n    popperConfig: popperConfig\n  })),\n      styles = _usePopper.styles,\n      attributes = _usePopper.attributes,\n      popper = _objectWithoutPropertiesLoose(_usePopper, [\"styles\", \"attributes\"]);\n\n  if (props.show) {\n    if (exited) setExited(false);\n  } else if (!props.transition && !exited) {\n    setExited(true);\n  }\n\n  var handleHidden = function handleHidden() {\n    setExited(true);\n\n    if (props.onExited) {\n      props.onExited.apply(props, arguments);\n    }\n  }; // Don't un-render the overlay while it's transitioning out.\n\n\n  var mountOverlay = props.show || Transition && !exited;\n  useRootClose(rootElement, props.onHide, {\n    disabled: !props.rootClose || props.rootCloseDisabled,\n    clickTrigger: props.rootCloseEvent\n  });\n\n  if (!mountOverlay) {\n    // Don't bother showing anything if we don't have to.\n    return null;\n  }\n\n  var child = props.children(_extends({}, popper, {\n    show: !!props.show,\n    props: _extends({}, attributes.popper, {\n      style: styles.popper,\n      ref: mergedRef\n    }),\n    arrowProps: _extends({}, attributes.arrow, {\n      style: styles.arrow,\n      ref: attachArrowRef\n    })\n  }));\n\n  if (Transition) {\n    var onExit = props.onExit,\n        onExiting = props.onExiting,\n        onEnter = props.onEnter,\n        onEntering = props.onEntering,\n        onEntered = props.onEntered;\n    child = /*#__PURE__*/React.createElement(Transition, {\n      \"in\": props.show,\n      appear: true,\n      onExit: onExit,\n      onExiting: onExiting,\n      onExited: handleHidden,\n      onEnter: onEnter,\n      onEntering: onEntering,\n      onEntered: onEntered\n    }, child);\n  }\n\n  return container ? /*#__PURE__*/ReactDOM.createPortal(child, container) : null;\n});\nOverlay.displayName = 'Overlay';\nOverlay.propTypes = {\n  /**\n   * Set the visibility of the Overlay\n   */\n  show: PropTypes.bool,\n\n  /** Specify where the overlay element is positioned in relation to the target element */\n  placement: PropTypes.oneOf(placements),\n\n  /**\n   * A DOM Element, Ref to an element, or function that returns either. The `target` element is where\n   * the overlay is positioned relative to.\n   */\n  target: PropTypes.any,\n\n  /**\n   * A DOM Element, Ref to an element, or function that returns either. The `container` will have the Portal children\n   * appended to it.\n   */\n  container: PropTypes.any,\n\n  /**\n   * Enables the Popper.js `flip` modifier, allowing the Overlay to\n   * automatically adjust it's placement in case of overlap with the viewport or toggle.\n   * Refer to the [flip docs](https://popper.js.org/popper-documentation.html#modifiers..flip.enabled) for more info\n   */\n  flip: PropTypes.bool,\n\n  /**\n   * A render prop that returns an element to overlay and position. See\n   * the [react-popper documentation](https://github.com/FezVrasta/react-popper#children) for more info.\n   *\n   * @type {Function ({\n   *   show: boolean,\n   *   placement: Placement,\n   *   update: () => void,\n   *   forceUpdate: () => void,\n   *   props: {\n   *     ref: (?HTMLElement) => void,\n   *     style: { [string]: string | number },\n   *     aria-labelledby: ?string\n   *     [string]: string | number,\n   *   },\n   *   arrowProps: {\n   *     ref: (?HTMLElement) => void,\n   *     style: { [string]: string | number },\n   *     [string]: string | number,\n   *   },\n   * }) => React.Element}\n   */\n  children: PropTypes.func.isRequired,\n\n  /**\n   * Control how much space there is between the edge of the boundary element and overlay.\n   * A convenience shortcut to setting `popperConfig.modfiers.preventOverflow.padding`\n   */\n  containerPadding: PropTypes.number,\n\n  /**\n   * A set of popper options and props passed directly to react-popper's Popper component.\n   */\n  popperConfig: PropTypes.object,\n\n  /**\n   * Specify whether the overlay should trigger `onHide` when the user clicks outside the overlay\n   */\n  rootClose: PropTypes.bool,\n\n  /**\n   * Specify event for toggling overlay\n   */\n  rootCloseEvent: PropTypes.oneOf(['click', 'mousedown']),\n\n  /**\n   * Specify disabled for disable RootCloseWrapper\n   */\n  rootCloseDisabled: PropTypes.bool,\n\n  /**\n   * A Callback fired by the Overlay when it wishes to be hidden.\n   *\n   * __required__ when `rootClose` is `true`.\n   *\n   * @type func\n   */\n  onHide: function onHide(props) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    if (props.rootClose) {\n      var _PropTypes$func;\n\n      return (_PropTypes$func = PropTypes.func).isRequired.apply(_PropTypes$func, [props].concat(args));\n    }\n\n    return PropTypes.func.apply(PropTypes, [props].concat(args));\n  },\n\n  /**\n   * A `react-transition-group@2.0.0` `<Transition/>` component\n   * used to animate the overlay as it changes visibility.\n   */\n  // @ts-ignore\n  transition: PropTypes.elementType,\n\n  /**\n   * Callback fired before the Overlay transitions in\n   */\n  onEnter: PropTypes.func,\n\n  /**\n   * Callback fired as the Overlay begins to transition in\n   */\n  onEntering: PropTypes.func,\n\n  /**\n   * Callback fired after the Overlay finishes transitioning in\n   */\n  onEntered: PropTypes.func,\n\n  /**\n   * Callback fired right before the Overlay transitions out\n   */\n  onExit: PropTypes.func,\n\n  /**\n   * Callback fired as the Overlay begins to transition out\n   */\n  onExiting: PropTypes.func,\n\n  /**\n   * Callback fired after the Overlay finishes transitioning out\n   */\n  onExited: PropTypes.func\n};\nexport default Overlay;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAEA;;;CAGC,GACD,IAAI,UAAU,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC,SAAU,KAAK,EAAE,QAAQ;IACnE,IAAI,OAAO,MAAM,IAAI,EACjB,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,wBAAwB,MAAM,gBAAgB,EAC9C,mBAAmB,0BAA0B,KAAK,IAAI,IAAI,uBAC1D,sBAAsB,MAAM,YAAY,EACxC,eAAe,wBAAwB,KAAK,IAAI,CAAC,IAAI,qBACrD,aAAa,MAAM,UAAU;IAEjC,IAAI,kBAAkB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD,KAC/B,cAAc,eAAe,CAAC,EAAE,EAChC,YAAY,eAAe,CAAC,EAAE;IAElC,IAAI,mBAAmB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD,KAChC,eAAe,gBAAgB,CAAC,EAAE,EAClC,iBAAiB,gBAAgB,CAAC,EAAE;IAExC,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD,EAAE,WAAW;IACzC,IAAI,YAAY,CAAA,GAAA,+JAAA,CAAA,UAAgB,AAAD,EAAE,MAAM,SAAS;IAChD,IAAI,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAgB,AAAD,EAAE,MAAM,MAAM;IAE1C,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,MAAM,IAAI,GAChC,SAAS,SAAS,CAAC,EAAE,EACrB,YAAY,SAAS,CAAC,EAAE;IAE5B,IAAI,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAS,AAAD,EAAE,QAAQ,aAAa,CAAA,GAAA,2KAAA,CAAA,UAA4B,AAAD,EAAE;QAC3E,WAAW;QACX,cAAc,CAAC,CAAC,MAAM,IAAI;QAC1B,kBAAkB,oBAAoB;QACtC,MAAM;QACN,QAAQ;QACR,cAAc;QACd,cAAc;IAChB,KACI,SAAS,WAAW,MAAM,EAC1B,aAAa,WAAW,UAAU,EAClC,SAAS,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,YAAY;QAAC;QAAU;KAAa;IAE/E,IAAI,MAAM,IAAI,EAAE;QACd,IAAI,QAAQ,UAAU;IACxB,OAAO,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,QAAQ;QACvC,UAAU;IACZ;IAEA,IAAI,eAAe,SAAS;QAC1B,UAAU;QAEV,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,KAAK,CAAC,OAAO;QAC9B;IACF,GAAG,4DAA4D;IAG/D,IAAI,eAAe,MAAM,IAAI,IAAI,cAAc,CAAC;IAChD,CAAA,GAAA,2JAAA,CAAA,UAAY,AAAD,EAAE,aAAa,MAAM,MAAM,EAAE;QACtC,UAAU,CAAC,MAAM,SAAS,IAAI,MAAM,iBAAiB;QACrD,cAAc,MAAM,cAAc;IACpC;IAEA,IAAI,CAAC,cAAc;QACjB,qDAAqD;QACrD,OAAO;IACT;IAEA,IAAI,QAAQ,MAAM,QAAQ,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,QAAQ;QAC9C,MAAM,CAAC,CAAC,MAAM,IAAI;QAClB,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,WAAW,MAAM,EAAE;YACrC,OAAO,OAAO,MAAM;YACpB,KAAK;QACP;QACA,YAAY,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,WAAW,KAAK,EAAE;YACzC,OAAO,OAAO,KAAK;YACnB,KAAK;QACP;IACF;IAEA,IAAI,YAAY;QACd,IAAI,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,aAAa,MAAM,UAAU,EAC7B,YAAY,MAAM,SAAS;QAC/B,QAAQ,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;YACnD,MAAM,MAAM,IAAI;YAChB,QAAQ;YACR,QAAQ;YACR,WAAW;YACX,UAAU;YACV,SAAS;YACT,YAAY;YACZ,WAAW;QACb,GAAG;IACL;IAEA,OAAO,YAAY,WAAW,GAAE,oKAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,OAAO,aAAa;AAC5E;AACA,QAAQ,WAAW,GAAG;AACtB,QAAQ,SAAS,GAAG;IAClB;;GAEC,GACD,MAAM,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEpB,sFAAsF,GACtF,WAAW,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC,qJAAA,CAAA,aAAU;IAErC;;;GAGC,GACD,QAAQ,yIAAA,CAAA,UAAS,CAAC,GAAG;IAErB;;;GAGC,GACD,WAAW,yIAAA,CAAA,UAAS,CAAC,GAAG;IAExB;;;;GAIC,GACD,MAAM,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEpB;;;;;;;;;;;;;;;;;;;;;GAqBC,GACD,UAAU,yIAAA,CAAA,UAAS,CAAC,IAAI,CAAC,UAAU;IAEnC;;;GAGC,GACD,kBAAkB,yIAAA,CAAA,UAAS,CAAC,MAAM;IAElC;;GAEC,GACD,cAAc,yIAAA,CAAA,UAAS,CAAC,MAAM;IAE9B;;GAEC,GACD,WAAW,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEzB;;GAEC,GACD,gBAAgB,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAAC;QAAS;KAAY;IAEtD;;GAEC,GACD,mBAAmB,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEjC;;;;;;GAMC,GACD,QAAQ,SAAS,OAAO,KAAK;QAC3B,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,MAAM,OAAQ;YAC1G,IAAI,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,KAAK;QAClC;QAEA,IAAI,MAAM,SAAS,EAAE;YACnB,IAAI;YAEJ,OAAO,CAAC,kBAAkB,yIAAA,CAAA,UAAS,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,iBAAiB;gBAAC;aAAM,CAAC,MAAM,CAAC;QAC7F;QAEA,OAAO,yIAAA,CAAA,UAAS,CAAC,IAAI,CAAC,KAAK,CAAC,yIAAA,CAAA,UAAS,EAAE;YAAC;SAAM,CAAC,MAAM,CAAC;IACxD;IAEA;;;GAGC,GACD,aAAa;IACb,YAAY,yIAAA,CAAA,UAAS,CAAC,WAAW;IAEjC;;GAEC,GACD,SAAS,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEvB;;GAEC,GACD,YAAY,yIAAA,CAAA,UAAS,CAAC,IAAI;IAE1B;;GAEC,GACD,WAAW,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEzB;;GAEC,GACD,QAAQ,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEtB;;GAEC,GACD,WAAW,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEzB;;GAEC,GACD,UAAU,yIAAA,CAAA,UAAS,CAAC,IAAI;AAC1B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/warning/warning.js"], "sourcesContent": ["/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\n\nvar warning = function() {};\n\nif (__DEV__) {\n  var printWarning = function printWarning(format, args) {\n    var len = arguments.length;\n    args = new Array(len > 1 ? len - 1 : 0);\n    for (var key = 1; key < len; key++) {\n      args[key - 1] = arguments[key];\n    }\n    var argIndex = 0;\n    var message = 'Warning: ' +\n      format.replace(/%s/g, function() {\n        return args[argIndex++];\n      });\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  }\n\n  warning = function(condition, format, args) {\n    var len = arguments.length;\n    args = new Array(len > 2 ? len - 2 : 0);\n    for (var key = 2; key < len; key++) {\n      args[key - 2] = arguments[key];\n    }\n    if (format === undefined) {\n      throw new Error(\n          '`warning(condition, format, ...args)` requires a warning ' +\n          'message argument'\n      );\n    }\n    if (!condition) {\n      printWarning.apply(null, [format].concat(args));\n    }\n  };\n}\n\nmodule.exports = warning;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAWa;AATd;AAEA;;;;;CAKC,GAED,IAAI,UAAU,oDAAyB;AAEvC,IAAI,UAAU,YAAY;AAE1B,wCAAa;IACX,IAAI,eAAe,SAAS,aAAa,MAAM,EAAE,IAAI;QACnD,IAAI,MAAM,UAAU,MAAM;QAC1B,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI;QACrC,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,MAAO;YAClC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI;QAChC;QACA,IAAI,WAAW;QACf,IAAI,UAAU,cACZ,OAAO,OAAO,CAAC,OAAO;YACpB,OAAO,IAAI,CAAC,WAAW;QACzB;QACF,IAAI,OAAO,YAAY,aAAa;YAClC,QAAQ,KAAK,CAAC;QAChB;QACA,IAAI;YACF,qCAAqC;YACrC,wEAAwE;YACxE,yDAAyD;YACzD,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,GAAG,CAAC;IACf;IAEA,UAAU,SAAS,SAAS,EAAE,MAAM,EAAE,IAAI;QACxC,IAAI,MAAM,UAAU,MAAM;QAC1B,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI;QACrC,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,MAAO;YAClC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI;QAChC;QACA,IAAI,WAAW,WAAW;YACxB,MAAM,IAAI,MACN,8DACA;QAEN;QACA,IAAI,CAAC,WAAW;YACd,aAAa,KAAK,CAAC,MAAM;gBAAC;aAAO,CAAC,MAAM,CAAC;QAC3C;IACF;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/memoize-one/dist/memoize-one.esm.js"], "sourcesContent": ["var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var cache = null;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {\n            return cache.lastResult;\n        }\n        var lastResult = resultFn.apply(this, newArgs);\n        cache = {\n            lastResult: lastResult,\n            lastArgs: newArgs,\n            lastThis: this,\n        };\n        return lastResult;\n    }\n    memoized.clear = function clear() {\n        cache = null;\n    };\n    return memoized;\n}\n\nexport { memoizeOne as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,YAAY,OAAO,KAAK,IACxB,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACJ,SAAS,QAAQ,KAAK,EAAE,MAAM;IAC1B,IAAI,UAAU,QAAQ;QAClB,OAAO;IACX;IACA,IAAI,UAAU,UAAU,UAAU,SAAS;QACvC,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,eAAe,SAAS,EAAE,UAAU;IACzC,IAAI,UAAU,MAAM,KAAK,WAAW,MAAM,EAAE;QACxC,OAAO;IACX;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACvC,IAAI,CAAC,QAAQ,SAAS,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,GAAG;YACvC,OAAO;QACX;IACJ;IACA,OAAO;AACX;AAEA,SAAS,WAAW,QAAQ,EAAE,OAAO;IACjC,IAAI,YAAY,KAAK,GAAG;QAAE,UAAU;IAAgB;IACpD,IAAI,QAAQ;IACZ,SAAS;QACL,IAAI,UAAU,EAAE;QAChB,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QAC/B;QACA,IAAI,SAAS,MAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ,SAAS,MAAM,QAAQ,GAAG;YACtE,OAAO,MAAM,UAAU;QAC3B;QACA,IAAI,aAAa,SAAS,KAAK,CAAC,IAAI,EAAE;QACtC,QAAQ;YACJ,YAAY;YACZ,UAAU;YACV,UAAU,IAAI;QAClB;QACA,OAAO;IACX;IACA,SAAS,KAAK,GAAG,SAAS;QACtB,QAAQ;IACZ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3882, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dayjs/plugin/isBetween.js"], "sourcesContent": ["!function(e,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i():\"function\"==typeof define&&define.amd?define(i):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isBetween=i()}(this,(function(){\"use strict\";return function(e,i,t){i.prototype.isBetween=function(e,i,s,f){var n=t(e),o=t(i),r=\"(\"===(f=f||\"()\")[0],u=\")\"===f[1];return(r?this.isAfter(n,s):!this.isBefore(n,s))&&(u?this.isBefore(o,s):!this.isAfter(o,s))||(r?this.isBefore(n,s):!this.isAfter(n,s))&&(u?this.isAfter(o,s):!this.isBefore(o,s))}}}));"], "names": [], "mappings": "AAAA,CAAC,SAAS,CAAC,EAAC,CAAC;IAAE,uCAAqD,OAAO,OAAO,GAAC;AAAoI,EAAE,IAAI,EAAE;IAAW;IAAa,OAAO,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,EAAE,SAAS,CAAC,SAAS,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAG,IAAE,EAAE,IAAG,IAAE,QAAM,CAAC,IAAE,KAAG,IAAI,CAAC,CAAC,EAAE,EAAC,IAAE,QAAM,CAAC,CAAC,EAAE;YAAC,OAAM,CAAC,IAAE,IAAI,CAAC,OAAO,CAAC,GAAE,KAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAE,EAAE,KAAG,CAAC,IAAE,IAAI,CAAC,QAAQ,CAAC,GAAE,KAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAE,EAAE,KAAG,CAAC,IAAE,IAAI,CAAC,QAAQ,CAAC,GAAE,KAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAE,EAAE,KAAG,CAAC,IAAE,IAAI,CAAC,OAAO,CAAC,GAAE,KAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAE,EAAE;QAAC;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3898, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dayjs/plugin/isSameOrAfter.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isSameOrAfter=t()}(this,(function(){\"use strict\";return function(e,t){t.prototype.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)}}}));"], "names": [], "mappings": "AAAA,CAAC,SAAS,CAAC,EAAC,CAAC;IAAE,uCAAqD,OAAO,OAAO,GAAC;AAAwI,EAAE,IAAI,EAAE;IAAW;IAAa,OAAO,SAAS,CAAC,EAAC,CAAC;QAAE,EAAE,SAAS,CAAC,aAAa,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAE,MAAI,IAAI,CAAC,OAAO,CAAC,GAAE;QAAE;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dayjs/plugin/isSameOrBefore.js"], "sourcesContent": ["!function(e,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i():\"function\"==typeof define&&define.amd?define(i):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isSameOrBefore=i()}(this,(function(){\"use strict\";return function(e,i){i.prototype.isSameOrBefore=function(e,i){return this.isSame(e,i)||this.isBefore(e,i)}}}));"], "names": [], "mappings": "AAAA,CAAC,SAAS,CAAC,EAAC,CAAC;IAAE,uCAAqD,OAAO,OAAO,GAAC;AAAyI,EAAE,IAAI,EAAE;IAAW;IAAa,OAAO,SAAS,CAAC,EAAC,CAAC;QAAE,EAAE,SAAS,CAAC,cAAc,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAE,MAAI,IAAI,CAAC,QAAQ,CAAC,GAAE;QAAE;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dayjs/plugin/localeData.js"], "sourcesContent": ["!function(n,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define(e):(n=\"undefined\"!=typeof globalThis?globalThis:n||self).dayjs_plugin_localeData=e()}(this,(function(){\"use strict\";return function(n,e,t){var r=e.prototype,o=function(n){return n&&(n.indexOf?n:n.s)},u=function(n,e,t,r,u){var i=n.name?n:n.$locale(),a=o(i[e]),s=o(i[t]),f=a||s.map((function(n){return n.slice(0,r)}));if(!u)return f;var d=i.weekStart;return f.map((function(n,e){return f[(e+(d||0))%7]}))},i=function(){return t.Ls[t.locale()]},a=function(n,e){return n.formats[e]||function(n){return n.replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(n,e,t){return e||t.slice(1)}))}(n.formats[e.toUpperCase()])},s=function(){var n=this;return{months:function(e){return e?e.format(\"MMMM\"):u(n,\"months\")},monthsShort:function(e){return e?e.format(\"MMM\"):u(n,\"monthsShort\",\"months\",3)},firstDayOfWeek:function(){return n.$locale().weekStart||0},weekdays:function(e){return e?e.format(\"dddd\"):u(n,\"weekdays\")},weekdaysMin:function(e){return e?e.format(\"dd\"):u(n,\"weekdaysMin\",\"weekdays\",2)},weekdaysShort:function(e){return e?e.format(\"ddd\"):u(n,\"weekdaysShort\",\"weekdays\",3)},longDateFormat:function(e){return a(n.$locale(),e)},meridiem:this.$locale().meridiem,ordinal:this.$locale().ordinal}};r.localeData=function(){return s.bind(this)()},t.localeData=function(){var n=i();return{firstDayOfWeek:function(){return n.weekStart||0},weekdays:function(){return t.weekdays()},weekdaysShort:function(){return t.weekdaysShort()},weekdaysMin:function(){return t.weekdaysMin()},months:function(){return t.months()},monthsShort:function(){return t.monthsShort()},longDateFormat:function(e){return a(n,e)},meridiem:n.meridiem,ordinal:n.ordinal}},t.months=function(){return u(i(),\"months\")},t.monthsShort=function(){return u(i(),\"monthsShort\",\"months\",3)},t.weekdays=function(n){return u(i(),\"weekdays\",null,null,n)},t.weekdaysShort=function(n){return u(i(),\"weekdaysShort\",\"weekdays\",3,n)},t.weekdaysMin=function(n){return u(i(),\"weekdaysMin\",\"weekdays\",2,n)}}}));"], "names": [], "mappings": "AAAA,CAAC,SAAS,CAAC,EAAC,CAAC;IAAE,uCAAqD,OAAO,OAAO,GAAC;AAAqI,EAAE,IAAI,EAAE;IAAW;IAAa,OAAO,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,EAAE,SAAS,EAAC,IAAE,SAAS,CAAC;YAAE,OAAO,KAAG,CAAC,EAAE,OAAO,GAAC,IAAE,EAAE,CAAC;QAAC,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,GAAC,IAAE,EAAE,OAAO,IAAG,IAAE,EAAE,CAAC,CAAC,EAAE,GAAE,IAAE,EAAE,CAAC,CAAC,EAAE,GAAE,IAAE,KAAG,EAAE,GAAG,CAAE,SAAS,CAAC;gBAAE,OAAO,EAAE,KAAK,CAAC,GAAE;YAAE;YAAI,IAAG,CAAC,GAAE,OAAO;YAAE,IAAI,IAAE,EAAE,SAAS;YAAC,OAAO,EAAE,GAAG,CAAE,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,CAAC,CAAC,CAAC,IAAE,CAAC,KAAG,CAAC,CAAC,IAAE,EAAE;YAAA;QAAG,GAAE,IAAE;YAAW,OAAO,EAAE,EAAE,CAAC,EAAE,MAAM,GAAG;QAAA,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC;YAAE,OAAO,EAAE,OAAO,CAAC,EAAE,IAAE,SAAS,CAAC;gBAAE,OAAO,EAAE,OAAO,CAAC,kCAAkC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;oBAAE,OAAO,KAAG,EAAE,KAAK,CAAC;gBAAE;YAAG,EAAE,EAAE,OAAO,CAAC,EAAE,WAAW,GAAG;QAAC,GAAE,IAAE;YAAW,IAAI,IAAE,IAAI;YAAC,OAAM;gBAAC,QAAO,SAAS,CAAC;oBAAE,OAAO,IAAE,EAAE,MAAM,CAAC,UAAQ,EAAE,GAAE;gBAAS;gBAAE,aAAY,SAAS,CAAC;oBAAE,OAAO,IAAE,EAAE,MAAM,CAAC,SAAO,EAAE,GAAE,eAAc,UAAS;gBAAE;gBAAE,gBAAe;oBAAW,OAAO,EAAE,OAAO,GAAG,SAAS,IAAE;gBAAC;gBAAE,UAAS,SAAS,CAAC;oBAAE,OAAO,IAAE,EAAE,MAAM,CAAC,UAAQ,EAAE,GAAE;gBAAW;gBAAE,aAAY,SAAS,CAAC;oBAAE,OAAO,IAAE,EAAE,MAAM,CAAC,QAAM,EAAE,GAAE,eAAc,YAAW;gBAAE;gBAAE,eAAc,SAAS,CAAC;oBAAE,OAAO,IAAE,EAAE,MAAM,CAAC,SAAO,EAAE,GAAE,iBAAgB,YAAW;gBAAE;gBAAE,gBAAe,SAAS,CAAC;oBAAE,OAAO,EAAE,EAAE,OAAO,IAAG;gBAAE;gBAAE,UAAS,IAAI,CAAC,OAAO,GAAG,QAAQ;gBAAC,SAAQ,IAAI,CAAC,OAAO,GAAG,OAAO;YAAA;QAAC;QAAE,EAAE,UAAU,GAAC;YAAW,OAAO,EAAE,IAAI,CAAC,IAAI;QAAG,GAAE,EAAE,UAAU,GAAC;YAAW,IAAI,IAAE;YAAI,OAAM;gBAAC,gBAAe;oBAAW,OAAO,EAAE,SAAS,IAAE;gBAAC;gBAAE,UAAS;oBAAW,OAAO,EAAE,QAAQ;gBAAE;gBAAE,eAAc;oBAAW,OAAO,EAAE,aAAa;gBAAE;gBAAE,aAAY;oBAAW,OAAO,EAAE,WAAW;gBAAE;gBAAE,QAAO;oBAAW,OAAO,EAAE,MAAM;gBAAE;gBAAE,aAAY;oBAAW,OAAO,EAAE,WAAW;gBAAE;gBAAE,gBAAe,SAAS,CAAC;oBAAE,OAAO,EAAE,GAAE;gBAAE;gBAAE,UAAS,EAAE,QAAQ;gBAAC,SAAQ,EAAE,OAAO;YAAA;QAAC,GAAE,EAAE,MAAM,GAAC;YAAW,OAAO,EAAE,KAAI;QAAS,GAAE,EAAE,WAAW,GAAC;YAAW,OAAO,EAAE,KAAI,eAAc,UAAS;QAAE,GAAE,EAAE,QAAQ,GAAC,SAAS,CAAC;YAAE,OAAO,EAAE,KAAI,YAAW,MAAK,MAAK;QAAE,GAAE,EAAE,aAAa,GAAC,SAAS,CAAC;YAAE,OAAO,EAAE,KAAI,iBAAgB,YAAW,GAAE;QAAE,GAAE,EAAE,WAAW,GAAC,SAAS,CAAC;YAAE,OAAO,EAAE,KAAI,eAAc,YAAW,GAAE;QAAE;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dayjs/plugin/localizedFormat.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_localizedFormat=t()}(this,(function(){\"use strict\";var e={LTS:\"h:mm:ss A\",LT:\"h:mm A\",L:\"MM/DD/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY h:mm A\",LLLL:\"dddd, MMMM D, YYYY h:mm A\"};return function(t,o,n){var r=o.prototype,i=r.format;n.en.formats=e,r.format=function(t){void 0===t&&(t=\"YYYY-MM-DDTHH:mm:ssZ\");var o=this.$locale().formats,n=function(t,o){return t.replace(/(\\[[^\\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,n,r){var i=r&&r.toUpperCase();return n||o[r]||e[r]||o[i].replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,o){return t||o.slice(1)}))}))}(t,void 0===o?{}:o);return i.call(this,n)}}}));"], "names": [], "mappings": "AAAA,CAAC,SAAS,CAAC,EAAC,CAAC;IAAE,uCAAqD,OAAO,OAAO,GAAC;AAA0I,EAAE,IAAI,EAAE;IAAW;IAAa,IAAI,IAAE;QAAC,KAAI;QAAY,IAAG;QAAS,GAAE;QAAa,IAAG;QAAe,KAAI;QAAsB,MAAK;IAA2B;IAAE,OAAO,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,MAAM;QAAC,EAAE,EAAE,CAAC,OAAO,GAAC,GAAE,EAAE,MAAM,GAAC,SAAS,CAAC;YAAE,KAAK,MAAI,KAAG,CAAC,IAAE,sBAAsB;YAAE,IAAI,IAAE,IAAI,CAAC,OAAO,GAAG,OAAO,EAAC,IAAE,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,OAAO,CAAC,qCAAqC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;oBAAE,IAAI,IAAE,KAAG,EAAE,WAAW;oBAAG,OAAO,KAAG,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,kCAAkC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;wBAAE,OAAO,KAAG,EAAE,KAAK,CAAC;oBAAE;gBAAG;YAAG,EAAE,GAAE,KAAK,MAAI,IAAE,CAAC,IAAE;YAAG,OAAO,EAAE,IAAI,CAAC,IAAI,EAAC;QAAE;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dayjs/plugin/minMax.js"], "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n():\"function\"==typeof define&&define.amd?define(n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_minMax=n()}(this,(function(){\"use strict\";return function(e,n,t){var i=function(e,n){if(!n||!n.length||1===n.length&&!n[0]||1===n.length&&Array.isArray(n[0])&&!n[0].length)return null;var t;1===n.length&&n[0].length>0&&(n=n[0]);t=(n=n.filter((function(e){return e})))[0];for(var i=1;i<n.length;i+=1)n[i].isValid()&&!n[i][e](t)||(t=n[i]);return t};t.max=function(){var e=[].slice.call(arguments,0);return i(\"isAfter\",e)},t.min=function(){var e=[].slice.call(arguments,0);return i(\"isBefore\",e)}}}));"], "names": [], "mappings": "AAAA,CAAC,SAAS,CAAC,EAAC,CAAC;IAAE,uCAAqD,OAAO,OAAO,GAAC;AAAiI,EAAE,IAAI,EAAE;IAAW;IAAa,OAAO,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,SAAS,CAAC,EAAC,CAAC;YAAE,IAAG,CAAC,KAAG,CAAC,EAAE,MAAM,IAAE,MAAI,EAAE,MAAM,IAAE,CAAC,CAAC,CAAC,EAAE,IAAE,MAAI,EAAE,MAAM,IAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAAE,KAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,EAAC,OAAO;YAAK,IAAI;YAAE,MAAI,EAAE,MAAM,IAAE,CAAC,CAAC,EAAE,CAAC,MAAM,GAAC,KAAG,CAAC,IAAE,CAAC,CAAC,EAAE;YAAE,IAAE,CAAC,IAAE,EAAE,MAAM,CAAE,SAAS,CAAC;gBAAE,OAAO;YAAC,EAAG,CAAC,CAAC,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,KAAG,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,MAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAI,CAAC,IAAE,CAAC,CAAC,EAAE;YAAE,OAAO;QAAC;QAAE,EAAE,GAAG,GAAC;YAAW,IAAI,IAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAU;YAAG,OAAO,EAAE,WAAU;QAAE,GAAE,EAAE,GAAG,GAAC;YAAW,IAAI,IAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAU;YAAG,OAAO,EAAE,YAAW;QAAE;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4089, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dayjs/plugin/utc.js"], "sourcesContent": ["!function(t,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i():\"function\"==typeof define&&define.amd?define(i):(t=\"undefined\"!=typeof globalThis?globalThis:t||self).dayjs_plugin_utc=i()}(this,(function(){\"use strict\";var t=\"minute\",i=/[+-]\\d\\d(?::?\\d\\d)?/g,e=/([+-]|\\d\\d)/g;return function(s,f,n){var u=f.prototype;n.utc=function(t){var i={date:t,utc:!0,args:arguments};return new f(i)},u.utc=function(i){var e=n(this.toDate(),{locale:this.$L,utc:!0});return i?e.add(this.utcOffset(),t):e},u.local=function(){return n(this.toDate(),{locale:this.$L,utc:!1})};var o=u.parse;u.parse=function(t){t.utc&&(this.$u=!0),this.$utils().u(t.$offset)||(this.$offset=t.$offset),o.call(this,t)};var r=u.init;u.init=function(){if(this.$u){var t=this.$d;this.$y=t.getUTCFullYear(),this.$M=t.getUTCMonth(),this.$D=t.getUTCDate(),this.$W=t.getUTCDay(),this.$H=t.getUTCHours(),this.$m=t.getUTCMinutes(),this.$s=t.getUTCSeconds(),this.$ms=t.getUTCMilliseconds()}else r.call(this)};var a=u.utcOffset;u.utcOffset=function(s,f){var n=this.$utils().u;if(n(s))return this.$u?0:n(this.$offset)?a.call(this):this.$offset;if(\"string\"==typeof s&&(s=function(t){void 0===t&&(t=\"\");var s=t.match(i);if(!s)return null;var f=(\"\"+s[0]).match(e)||[\"-\",0,0],n=f[0],u=60*+f[1]+ +f[2];return 0===u?0:\"+\"===n?u:-u}(s),null===s))return this;var u=Math.abs(s)<=16?60*s:s,o=this;if(f)return o.$offset=u,o.$u=0===s,o;if(0!==s){var r=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(o=this.local().add(u+r,t)).$offset=u,o.$x.$localOffset=r}else o=this.utc();return o};var h=u.format;u.format=function(t){var i=t||(this.$u?\"YYYY-MM-DDTHH:mm:ss[Z]\":\"\");return h.call(this,i)},u.valueOf=function(){var t=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*t},u.isUTC=function(){return!!this.$u},u.toISOString=function(){return this.toDate().toISOString()},u.toString=function(){return this.toDate().toUTCString()};var l=u.toDate;u.toDate=function(t){return\"s\"===t&&this.$offset?n(this.format(\"YYYY-MM-DD HH:mm:ss:SSS\")).toDate():l.call(this)};var c=u.diff;u.diff=function(t,i,e){if(t&&this.$u===t.$u)return c.call(this,t,i,e);var s=this.local(),f=n(t).local();return c.call(s,f,i,e)}}}));"], "names": [], "mappings": "AAAA,CAAC,SAAS,CAAC,EAAC,CAAC;IAAE,uCAAqD,OAAO,OAAO,GAAC;AAA8H,EAAE,IAAI,EAAE;IAAW;IAAa,IAAI,IAAE,UAAS,IAAE,wBAAuB,IAAE;IAAe,OAAO,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,EAAE,SAAS;QAAC,EAAE,GAAG,GAAC,SAAS,CAAC;YAAE,IAAI,IAAE;gBAAC,MAAK;gBAAE,KAAI,CAAC;gBAAE,MAAK;YAAS;YAAE,OAAO,IAAI,EAAE;QAAE,GAAE,EAAE,GAAG,GAAC,SAAS,CAAC;YAAE,IAAI,IAAE,EAAE,IAAI,CAAC,MAAM,IAAG;gBAAC,QAAO,IAAI,CAAC,EAAE;gBAAC,KAAI,CAAC;YAAC;YAAG,OAAO,IAAE,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,IAAG,KAAG;QAAC,GAAE,EAAE,KAAK,GAAC;YAAW,OAAO,EAAE,IAAI,CAAC,MAAM,IAAG;gBAAC,QAAO,IAAI,CAAC,EAAE;gBAAC,KAAI,CAAC;YAAC;QAAE;QAAE,IAAI,IAAE,EAAE,KAAK;QAAC,EAAE,KAAK,GAAC,SAAS,CAAC;YAAE,EAAE,GAAG,IAAE,CAAC,IAAI,CAAC,EAAE,GAAC,CAAC,CAAC,GAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,KAAG,CAAC,IAAI,CAAC,OAAO,GAAC,EAAE,OAAO,GAAE,EAAE,IAAI,CAAC,IAAI,EAAC;QAAE;QAAE,IAAI,IAAE,EAAE,IAAI;QAAC,EAAE,IAAI,GAAC;YAAW,IAAG,IAAI,CAAC,EAAE,EAAC;gBAAC,IAAI,IAAE,IAAI,CAAC,EAAE;gBAAC,IAAI,CAAC,EAAE,GAAC,EAAE,cAAc,IAAG,IAAI,CAAC,EAAE,GAAC,EAAE,WAAW,IAAG,IAAI,CAAC,EAAE,GAAC,EAAE,UAAU,IAAG,IAAI,CAAC,EAAE,GAAC,EAAE,SAAS,IAAG,IAAI,CAAC,EAAE,GAAC,EAAE,WAAW,IAAG,IAAI,CAAC,EAAE,GAAC,EAAE,aAAa,IAAG,IAAI,CAAC,EAAE,GAAC,EAAE,aAAa,IAAG,IAAI,CAAC,GAAG,GAAC,EAAE,kBAAkB;YAAE,OAAM,EAAE,IAAI,CAAC,IAAI;QAAC;QAAE,IAAI,IAAE,EAAE,SAAS;QAAC,EAAE,SAAS,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,IAAI,CAAC,MAAM,GAAG,CAAC;YAAC,IAAG,EAAE,IAAG,OAAO,IAAI,CAAC,EAAE,GAAC,IAAE,EAAE,IAAI,CAAC,OAAO,IAAE,EAAE,IAAI,CAAC,IAAI,IAAE,IAAI,CAAC,OAAO;YAAC,IAAG,YAAU,OAAO,KAAG,CAAC,IAAE,SAAS,CAAC;gBAAE,KAAK,MAAI,KAAG,CAAC,IAAE,EAAE;gBAAE,IAAI,IAAE,EAAE,KAAK,CAAC;gBAAG,IAAG,CAAC,GAAE,OAAO;gBAAK,IAAI,IAAE,CAAC,KAAG,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,MAAI;oBAAC;oBAAI;oBAAE;iBAAE,EAAC,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,KAAG,CAAC,CAAC,CAAC,EAAE,GAAE,CAAC,CAAC,CAAC,EAAE;gBAAC,OAAO,MAAI,IAAE,IAAE,QAAM,IAAE,IAAE,CAAC;YAAC,EAAE,IAAG,SAAO,CAAC,GAAE,OAAO,IAAI;YAAC,IAAI,IAAE,KAAK,GAAG,CAAC,MAAI,KAAG,KAAG,IAAE,GAAE,IAAE,IAAI;YAAC,IAAG,GAAE,OAAO,EAAE,OAAO,GAAC,GAAE,EAAE,EAAE,GAAC,MAAI,GAAE;YAAE,IAAG,MAAI,GAAE;gBAAC,IAAI,IAAE,IAAI,CAAC,EAAE,GAAC,IAAI,CAAC,MAAM,GAAG,iBAAiB,KAAG,CAAC,IAAE,IAAI,CAAC,SAAS;gBAAG,CAAC,IAAE,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAE,GAAE,EAAE,EAAE,OAAO,GAAC,GAAE,EAAE,EAAE,CAAC,YAAY,GAAC;YAAC,OAAM,IAAE,IAAI,CAAC,GAAG;YAAG,OAAO;QAAC;QAAE,IAAI,IAAE,EAAE,MAAM;QAAC,EAAE,MAAM,GAAC,SAAS,CAAC;YAAE,IAAI,IAAE,KAAG,CAAC,IAAI,CAAC,EAAE,GAAC,2BAAyB,EAAE;YAAE,OAAO,EAAE,IAAI,CAAC,IAAI,EAAC;QAAE,GAAE,EAAE,OAAO,GAAC;YAAW,IAAI,IAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,IAAE,IAAE,IAAI,CAAC,OAAO,GAAC,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,IAAE,IAAI,CAAC,EAAE,CAAC,iBAAiB,EAAE;YAAE,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,KAAG,MAAI;QAAC,GAAE,EAAE,KAAK,GAAC;YAAW,OAAM,CAAC,CAAC,IAAI,CAAC,EAAE;QAAA,GAAE,EAAE,WAAW,GAAC;YAAW,OAAO,IAAI,CAAC,MAAM,GAAG,WAAW;QAAE,GAAE,EAAE,QAAQ,GAAC;YAAW,OAAO,IAAI,CAAC,MAAM,GAAG,WAAW;QAAE;QAAE,IAAI,IAAE,EAAE,MAAM;QAAC,EAAE,MAAM,GAAC,SAAS,CAAC;YAAE,OAAM,QAAM,KAAG,IAAI,CAAC,OAAO,GAAC,EAAE,IAAI,CAAC,MAAM,CAAC,4BAA4B,MAAM,KAAG,EAAE,IAAI,CAAC,IAAI;QAAC;QAAE,IAAI,IAAE,EAAE,IAAI;QAAC,EAAE,IAAI,GAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAG,KAAG,IAAI,CAAC,EAAE,KAAG,EAAE,EAAE,EAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAC,GAAE,GAAE;YAAG,IAAI,IAAE,IAAI,CAAC,KAAK,IAAG,IAAE,EAAE,GAAG,KAAK;YAAG,OAAO,EAAE,IAAI,CAAC,GAAE,GAAE,GAAE;QAAE;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/dayjs/plugin/isLeapYear.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isLeapYear=t()}(this,(function(){\"use strict\";return function(e,t){t.prototype.isLeapYear=function(){return this.$y%4==0&&this.$y%100!=0||this.$y%400==0}}}));"], "names": [], "mappings": "AAAA,CAAC,SAAS,CAAC,EAAC,CAAC;IAAE,uCAAqD,OAAO,OAAO,GAAC;AAAqI,EAAE,IAAI,EAAE;IAAW;IAAa,OAAO,SAAS,CAAC,EAAC,CAAC;QAAE,EAAE,SAAS,CAAC,UAAU,GAAC;YAAW,OAAO,IAAI,CAAC,EAAE,GAAC,KAAG,KAAG,IAAI,CAAC,EAAE,GAAC,OAAK,KAAG,IAAI,CAAC,EAAE,GAAC,OAAK;QAAC;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4196, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4242, "column": 0}, "map": {"version": 3, "file": "funnel.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/lucide-react/src/icons/funnel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z',\n      key: 'sc7q7i',\n    },\n  ],\n];\n\n/**\n * @component @name Funnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBhMSAxIDAgMCAwIC41NTMuODk1bDIgMUExIDEgMCAwIDAgMTQgMjF2LTdhMiAyIDAgMCAxIC41MTctMS4zNDFMMjEuNzQgNC42N0ExIDEgMCAwIDAgMjEgM0gzYTEgMSAwIDAgMC0uNzQyIDEuNjdsNy4yMjUgNy45ODlBMiAyIDAgMCAxIDEwIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/funnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Funnel = createLucideIcon('funnel', __iconNode);\n\nexport default Funnel;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4281, "column": 0}, "map": {"version": 3, "file": "download.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/lucide-react/src/icons/download.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 15V3', key: 'm9g1x1' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['path', { d: 'm7 10 5 5 5-5', key: 'brsn70' }],\n];\n\n/**\n * @component @name Download\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTVWMyIgLz4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cGF0aCBkPSJtNyAxMCA1IDUgNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/download\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Download = createLucideIcon('download', __iconNode);\n\nexport default Download;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4334, "column": 0}, "map": {"version": 3, "file": "share.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/lucide-react/src/icons/share.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 2v13', key: '1km8f5' }],\n  ['path', { d: 'm16 6-4-4-4 4', key: '13yo43' }],\n  ['path', { d: 'M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8', key: '1b2hhj' }],\n];\n\n/**\n * @component @name Share\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnYxMyIgLz4KICA8cGF0aCBkPSJtMTYgNi00LTQtNCA0IiAvPgogIDxwYXRoIGQ9Ik00IDEydjhhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0ydi04IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/share\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Share = createLucideIcon('share', __iconNode);\n\nexport default Share;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4387, "column": 0}, "map": {"version": 3, "file": "map-pin.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/lucide-react/src/icons/map-pin.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0',\n      key: '1r0f0z',\n    },\n  ],\n  ['circle', { cx: '12', cy: '10', r: '3', key: 'ilqhr7' }],\n];\n\n/**\n * @component @name MapPin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBjMCA0Ljk5My01LjUzOSAxMC4xOTMtNy4zOTkgMTEuNzk5YTEgMSAwIDAgMS0xLjIwMiAwQzkuNTM5IDIwLjE5MyA0IDE0Ljk5MyA0IDEwYTggOCAwIDAgMSAxNiAwIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map-pin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPin = createLucideIcon('map-pin', __iconNode);\n\nexport default MapPin;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4435, "column": 0}, "map": {"version": 3, "file": "dollar-sign.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACpF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4484, "column": 0}, "map": {"version": 3, "file": "star.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/lucide-react/src/icons/star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z',\n      key: 'r04s7s',\n    },\n  ],\n];\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('star', __iconNode);\n\nexport default Star;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4523, "column": 0}, "map": {"version": 3, "file": "users.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Ideas%20and%20planning/activity-planner/node_modules/lucide-react/src/icons/users.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['path', { d: 'M16 3.128a4 4 0 0 1 0 7.744', key: '16gr8j' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n];\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8cGF0aCBkPSJNMTYgMy4xMjhhNCA0IDAgMCAxIDAgNy43NDQiIC8+CiAgPHBhdGggZD0iTTIyIDIxdi0yYTQgNCAwIDAgMC0zLTMuODciIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('users', __iconNode);\n\nexport default Users;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3D;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,OAAA;QAAS,CAAA;KAAA;CACvD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}