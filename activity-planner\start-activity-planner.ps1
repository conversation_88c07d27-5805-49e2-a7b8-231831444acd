# Activity Planner Startup Script
Write-Host "🎯 Starting Activity Planner..." -ForegroundColor Green
Write-Host ""

# Change to the script directory
Set-Location $PSScriptRoot

# Start the development server in background
Write-Host "📦 Starting development server..." -ForegroundColor Yellow
$job = Start-Job -ScriptBlock {
    Set-Location $using:PSScriptRoot
    npm run dev
}

# Wait a moment for server to start
Write-Host "⏳ Waiting for server to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Test if server is running
$maxAttempts = 10
$attempt = 0
$serverRunning = $false

while ($attempt -lt $maxAttempts -and -not $serverRunning) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 2 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            $serverRunning = $true
        }
    }
    catch {
        $attempt++
        Start-Sleep -Seconds 2
    }
}

if ($serverRunning) {
    Write-Host "✅ Server is running!" -ForegroundColor Green
    Write-Host "🌐 Opening Activity Planner in your browser..." -ForegroundColor Cyan
    
    # Open the browser
    Start-Process "http://localhost:3000"
    
    Write-Host ""
    Write-Host "🎉 Activity Planner is now running!" -ForegroundColor Green
    Write-Host "📍 URL: http://localhost:3000" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 Tips:" -ForegroundColor Yellow
    Write-Host "   • Keep this window open while using the app" -ForegroundColor White
    Write-Host "   • Press Ctrl+C to stop the server" -ForegroundColor White
    Write-Host "   • The app will automatically refresh when you make changes" -ForegroundColor White
    Write-Host ""
    
    # Keep the script running and monitor the job
    try {
        Write-Host "🔄 Server is running... (Press Ctrl+C to stop)" -ForegroundColor Cyan
        while ($job.State -eq "Running") {
            Start-Sleep -Seconds 1
        }
    }
    finally {
        Write-Host "🛑 Stopping server..." -ForegroundColor Red
        Stop-Job $job -ErrorAction SilentlyContinue
        Remove-Job $job -ErrorAction SilentlyContinue
    }
}
else {
    Write-Host "❌ Failed to start server. Please check for errors." -ForegroundColor Red
    Stop-Job $job -ErrorAction SilentlyContinue
    Remove-Job $job -ErrorAction SilentlyContinue
}

Write-Host "👋 Activity Planner stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
