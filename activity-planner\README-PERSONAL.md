# 🎯 Personal Activity Planner

A comprehensive activity planning app designed for personal use to organize, schedule, and track your activities with calendar integration, budget tracking, and more.

## 🚀 Quick Start

### Option 1: Desktop Shortcut (Recommended)
1. **Create Desktop Shortcut**: Double-click `create-desktop-shortcut.vbs`
2. **Launch App**: Double-click the "Activity Planner" shortcut on your desktop
3. **Start Planning**: The app will open automatically in your browser at http://localhost:3000

### Option 2: Manual Start
1. **PowerShell**: Double-click `start-activity-planner.ps1`
2. **Command Line**: Double-click `start-activity-planner.bat`
3. **Manual**: Open terminal, run `npm run dev`, then visit http://localhost:3000

## ✨ Features

### 📋 Activity Management
- **Add Activities**: Create detailed activity ideas with descriptions, locations, costs
- **Categories**: Organize by type (Date Ideas, Outdoor, Cultural, Food, etc.)
- **Tags & Difficulty**: Tag activities and set difficulty levels
- **File Attachments**: Upload tickets (PDFs), images, and save URLs

### 📅 Calendar Integration
- **Schedule Activities**: Plan activities on specific dates and times
- **Calendar Views**: Month, week, and day views
- **Status Tracking**: Track planned, in-progress, completed, and cancelled activities
- **Recurring Activities**: Set up regular activities (weekly movie nights, etc.)

### 💰 Budget Tracking
- **Cost Estimation**: Set estimated costs for activities
- **Actual Spending**: Track real costs after completion
- **Budget Analytics**: View spending patterns and statistics

### ⭐ Rating & Reviews
- **Rate Activities**: 1-5 star rating system after completion
- **Add Notes**: Detailed notes and memories from activities
- **Photo Gallery**: Upload photos from completed activities

### 🎯 Wishlist & Goals
- **Dream Activities**: Maintain a wishlist of future activities
- **Priority Levels**: Set high, medium, low priorities
- **Target Dates**: Set goals for when you want to do activities

### 📊 Analytics & Insights
- **Activity Stats**: Total activities, completion rates, spending
- **Category Analysis**: See your favorite types of activities
- **Monthly Trends**: Track your activity patterns over time
- **Achievements**: Unlock badges for milestones

### 🌤️ Weather Integration
- **Weather Alerts**: Check weather for outdoor activities
- **Planning Assistance**: Get weather info when scheduling

### 👥 Social Features
- **Attendees**: Track who joined you for activities
- **Sharing**: Share activity ideas and plans
- **Activity History**: Complete timeline of your activities

## 🛠️ Technical Details

- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Calendar**: React Big Calendar with moment.js
- **Icons**: Lucide React icons
- **Database Ready**: Supabase integration prepared (optional)

## 📁 Project Structure

```
activity-planner/
├── src/
│   ├── app/                 # Next.js app router pages
│   │   ├── page.tsx         # Dashboard
│   │   ├── activities/      # Activity management
│   │   ├── calendar/        # Calendar view
│   │   ├── wishlist/        # Wishlist management
│   │   └── analytics/       # Analytics & insights
│   ├── components/          # Reusable UI components
│   ├── lib/                 # Utilities and helpers
│   └── types/               # TypeScript type definitions
├── start-activity-planner.ps1   # PowerShell startup script
├── start-activity-planner.bat   # Batch startup script
└── create-desktop-shortcut.vbs  # Desktop shortcut creator
```

## 🎨 Customization

The app is designed for personal use and can be easily customized:

- **Colors**: Edit `src/app/globals.css` to change the color scheme
- **Categories**: Modify categories in `src/lib/supabase.ts`
- **Features**: Add or remove features by editing the respective page components

## 💾 Data Storage

Currently uses mock data for demonstration. To add persistent storage:

1. **Set up Supabase** (recommended for cloud storage)
2. **Use Local Database** (SQLite for offline use)
3. **File-based Storage** (JSON files for simple setup)

## 🔧 Troubleshooting

### Server Won't Start
- Make sure Node.js is installed
- Run `npm install` in the project directory
- Check if port 3000 is available

### Browser Doesn't Open
- Manually visit http://localhost:3000
- Check Windows firewall settings
- Try a different browser

### Shortcut Issues
- Run PowerShell as administrator
- Check execution policy: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

## 🎉 Enjoy Planning!

Your personal activity planner is ready to help you:
- 📝 Organize activity ideas
- 📅 Schedule and plan activities
- 💰 Track spending and budgets
- ⭐ Rate and remember experiences
- 📊 Analyze your activity patterns
- 🎯 Achieve your activity goals

Happy planning! 🎊
