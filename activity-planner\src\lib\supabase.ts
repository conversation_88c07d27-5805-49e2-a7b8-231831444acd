import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export function createSupabaseClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Database schema creation SQL (for reference)
export const DATABASE_SCHEMA = `
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (handled by <PERSON><PERSON><PERSON> Auth)
-- We'll use auth.users and create a profiles table for additional data

-- Profiles table for additional user data
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  name TEXT,
  avatar TEXT,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activity categories
CREATE TABLE activity_categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  icon TEXT NOT NULL,
  color TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activities
CREATE TABLE activities (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  category_id UUID REFERENCES activity_categories(id),
  location TEXT,
  url TEXT,
  estimated_cost DECIMAL(10,2),
  duration INTEGER, -- in minutes
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
  is_outdoor BOOLEAN DEFAULT FALSE,
  tags TEXT[],
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Planned activities
CREATE TABLE planned_activities (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  activity_id UUID REFERENCES activities(id),
  scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
  actual_date TIMESTAMP WITH TIME ZONE,
  status TEXT CHECK (status IN ('planned', 'in-progress', 'completed', 'cancelled')) DEFAULT 'planned',
  notes TEXT,
  actual_cost DECIMAL(10,2),
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  photos TEXT[],
  attendees TEXT[],
  weather_condition TEXT,
  reminder_set BOOLEAN DEFAULT FALSE,
  reminder_time TIMESTAMP WITH TIME ZONE,
  is_recurring BOOLEAN DEFAULT FALSE,
  recurring_pattern JSONB,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- File attachments
CREATE TABLE file_attachments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  activity_id UUID REFERENCES activities(id),
  planned_activity_id UUID REFERENCES planned_activities(id),
  file_name TEXT NOT NULL,
  file_type TEXT CHECK (file_type IN ('pdf', 'image', 'document')),
  file_url TEXT NOT NULL,
  file_size BIGINT,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Budgets
CREATE TABLE budgets (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  total_budget DECIMAL(10,2) NOT NULL,
  spent_amount DECIMAL(10,2) DEFAULT 0,
  period TEXT CHECK (period IN ('monthly', 'yearly', 'custom')) DEFAULT 'monthly',
  start_date DATE NOT NULL,
  end_date DATE,
  categories UUID[],
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activity history
CREATE TABLE activity_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  planned_activity_id UUID REFERENCES planned_activities(id),
  action TEXT CHECK (action IN ('created', 'updated', 'completed', 'cancelled', 'rescheduled')),
  details TEXT,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Wishlist
CREATE TABLE wishlist (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  priority TEXT CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
  estimated_cost DECIMAL(10,2),
  target_date DATE,
  location TEXT,
  tags TEXT[],
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications
CREATE TABLE notifications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  type TEXT CHECK (type IN ('reminder', 'weather_alert', 'budget_alert', 'sharing')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Shared activities
CREATE TABLE shared_activities (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  activity_id UUID REFERENCES activities(id),
  shared_by UUID REFERENCES auth.users(id),
  shared_with UUID[],
  message TEXT,
  permissions TEXT CHECK (permissions IN ('view', 'edit')) DEFAULT 'view',
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security (RLS) policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE planned_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE file_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlist ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE shared_activities ENABLE ROW LEVEL SECURITY;

-- Policies for profiles
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Policies for activities
CREATE POLICY "Users can view own activities" ON activities FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own activities" ON activities FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own activities" ON activities FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own activities" ON activities FOR DELETE USING (auth.uid() = user_id);

-- Similar policies for other tables...
-- (Add more policies as needed)

-- Insert default categories
INSERT INTO activity_categories (name, icon, color, description) VALUES
('Date Ideas', '💕', '#FF69B4', 'Romantic activities for couples'),
('Outdoor Adventures', '🌲', '#228B22', 'Activities in nature and outdoors'),
('Indoor Fun', '🏠', '#4169E1', 'Activities you can do indoors'),
('Cultural Experiences', '🎭', '#800080', 'Museums, theaters, and cultural events'),
('Food & Dining', '🍽️', '#FF6347', 'Restaurants, cooking, and food experiences'),
('Sports & Fitness', '⚽', '#FF4500', 'Physical activities and sports'),
('Relaxation', '🧘', '#20B2AA', 'Peaceful and calming activities'),
('Entertainment', '🎬', '#FFD700', 'Movies, shows, and entertainment'),
('Travel', '✈️', '#1E90FF', 'Travel and vacation activities'),
('Learning', '📚', '#32CD32', 'Educational and skill-building activities'),
('Social Events', '👥', '#FF1493', 'Activities with friends and groups'),
('Adventure Sports', '🎢', '#DC143C', 'Thrilling and adventurous activities');
`;
