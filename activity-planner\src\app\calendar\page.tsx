"use client"

import { useState } from "react"
import { Calendar, momentLocalizer } from "react-big-calendar"
import moment from "moment"
import "react-big-calendar/lib/css/react-big-calendar.css"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  Plus, 
  Filter, 
  Download, 
  Share,
  MapPin,
  Clock,
  DollarSign,
  Star,
  Users,
  Calendar as CalendarIcon
} from "lucide-react"
import { CalendarEvent, PlannedActivity } from "@/types"
import { getCategoryIcon, formatCurrency, getWeatherIcon } from "@/lib/utils"

const localizer = momentLocalizer(moment)

export default function CalendarPage() {
  const [view, setView] = useState<'month' | 'week' | 'day'>('month')
  const [selectedEvent, setSelectedEvent] = useState<PlannedActivity | null>(null)

  // Mock data - in real app this would come from Supabase
  const plannedActivities: PlannedActivity[] = [
    {
      id: "1",
      activityId: "1",
      activity: {
        id: "1",
        title: "Romantic Dinner",
        description: "Fine dining at Chez Laurent",
        category: { id: "date", name: "Date Ideas", icon: "💕", color: "#FF69B4" },
        location: "Downtown French Quarter",
        estimatedCost: 150.00,
        isOutdoor: false,
        tags: ["romantic", "fine-dining"],
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: "user1"
      },
      scheduledDate: new Date("2025-07-10T19:00:00"),
      status: "planned",
      notes: "Anniversary dinner - make sure to request window table",
      attendees: ["Partner"],
      weatherCondition: "clear",
      reminderSet: true,
      reminderTime: new Date("2025-07-10T17:00:00"),
      isRecurring: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: "user1"
    },
    {
      id: "2",
      activityId: "2",
      activity: {
        id: "2",
        title: "Hiking Adventure",
        description: "Blue Ridge Trail hike",
        category: { id: "outdoor", name: "Outdoor", icon: "🌲", color: "#228B22" },
        location: "Blue Ridge National Park",
        estimatedCost: 0,
        isOutdoor: true,
        tags: ["hiking", "nature"],
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: "user1"
      },
      scheduledDate: new Date("2025-07-12T08:00:00"),
      status: "planned",
      attendees: ["Friend 1", "Friend 2"],
      weatherCondition: "partly-cloudy",
      reminderSet: true,
      reminderTime: new Date("2025-07-12T07:00:00"),
      isRecurring: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: "user1"
    },
    {
      id: "3",
      activityId: "3",
      activity: {
        id: "3",
        title: "Movie Night",
        description: "Latest Marvel movie",
        category: { id: "entertainment", name: "Entertainment", icon: "🎬", color: "#FFD700" },
        location: "Home",
        estimatedCost: 15.00,
        isOutdoor: false,
        tags: ["movies", "relaxing"],
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: "user1"
      },
      scheduledDate: new Date("2025-07-15T20:00:00"),
      status: "planned",
      attendees: ["Partner"],
      reminderSet: false,
      isRecurring: true,
      recurringPattern: {
        frequency: "weekly",
        interval: 1,
        daysOfWeek: [5], // Friday
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: "user1"
    },
    {
      id: "4",
      activityId: "4",
      activity: {
        id: "4",
        title: "Art Museum Visit",
        description: "Modern art exhibition",
        category: { id: "cultural", name: "Cultural", icon: "🎭", color: "#800080" },
        location: "Metropolitan Art Museum",
        estimatedCost: 25.00,
        isOutdoor: false,
        tags: ["art", "culture"],
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: "user1"
      },
      scheduledDate: new Date("2025-07-08T14:00:00"),
      actualDate: new Date("2025-07-08T14:30:00"),
      status: "completed",
      rating: 5,
      actualCost: 25.00,
      notes: "Amazing exhibition! The interactive displays were fantastic.",
      photos: ["/photos/museum1.jpg", "/photos/museum2.jpg"],
      attendees: ["Partner"],
      reminderSet: false,
      isRecurring: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: "user1"
    }
  ]

  // Convert planned activities to calendar events
  const events: CalendarEvent[] = plannedActivities.map(activity => ({
    id: activity.id,
    title: activity.activity?.title || "Untitled Activity",
    start: activity.scheduledDate,
    end: new Date(activity.scheduledDate.getTime() + (activity.activity?.duration || 60) * 60000),
    resource: activity
  }))

  const handleSelectEvent = (event: CalendarEvent) => {
    setSelectedEvent(event.resource)
  }

  const handleSelectSlot = ({ start, end }: { start: Date; end: Date }) => {
    // Handle creating new event
    console.log("Selected slot:", start, end)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planned": return "border-l-blue-500"
      case "completed": return "border-l-green-500"
      case "cancelled": return "border-l-red-500"
      case "in-progress": return "border-l-yellow-500"
      default: return "border-l-gray-500"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Calendar</h1>
          <p className="text-muted-foreground">Schedule and manage your activities</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline">
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Schedule Activity
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Calendar */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Activity Calendar</CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant={view === "month" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setView("month")}
                  >
                    Month
                  </Button>
                  <Button
                    variant={view === "week" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setView("week")}
                  >
                    Week
                  </Button>
                  <Button
                    variant={view === "day" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setView("day")}
                  >
                    Day
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-[600px]">
                <Calendar
                  localizer={localizer}
                  events={events}
                  startAccessor="start"
                  endAccessor="end"
                  view={view}
                  onView={setView}
                  onSelectEvent={handleSelectEvent}
                  onSelectSlot={handleSelectSlot}
                  selectable
                  eventPropGetter={(event) => ({
                    className: `${getStatusColor(event.resource?.status || "")} border-l-4`,
                    style: {
                      backgroundColor: event.resource?.activity?.category?.color + "20" || "#3174ad20",
                      borderColor: event.resource?.activity?.category?.color || "#3174ad",
                      color: "#000"
                    }
                  })}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Upcoming Activities */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Upcoming This Week</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {plannedActivities
                .filter(activity => {
                  const now = new Date()
                  const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
                  return activity.scheduledDate >= now && activity.scheduledDate <= weekFromNow && activity.status === "planned"
                })
                .slice(0, 3)
                .map((activity) => (
                  <div key={activity.id} className="p-3 border rounded-lg space-y-2">
                    <div className="flex items-center gap-2">
                      <span>{getCategoryIcon(activity.activity?.category?.name || "")}</span>
                      <h4 className="font-medium text-sm">{activity.activity?.title}</h4>
                    </div>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="h-3 w-3" />
                        <span>{moment(activity.scheduledDate).format("MMM D, h:mm A")}</span>
                      </div>
                      {activity.activity?.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          <span>{activity.activity.location}</span>
                        </div>
                      )}
                      {activity.weatherCondition && activity.activity?.isOutdoor && (
                        <div className="flex items-center gap-1">
                          <span>{getWeatherIcon(activity.weatherCondition)}</span>
                          <span className="capitalize">{activity.weatherCondition}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
            </CardContent>
          </Card>

          {/* Activity Details */}
          {selectedEvent && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <span>{getCategoryIcon(selectedEvent.activity?.category?.name || "")}</span>
                  Activity Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold">{selectedEvent.activity?.title}</h3>
                  <p className="text-sm text-muted-foreground">{selectedEvent.activity?.description}</p>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4" />
                    <span>{moment(selectedEvent.scheduledDate).format("MMMM D, YYYY at h:mm A")}</span>
                  </div>
                  
                  {selectedEvent.activity?.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>{selectedEvent.activity.location}</span>
                    </div>
                  )}

                  {selectedEvent.activity?.estimatedCost && (
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      <span>{formatCurrency(selectedEvent.activity.estimatedCost)}</span>
                    </div>
                  )}

                  {selectedEvent.attendees && selectedEvent.attendees.length > 0 && (
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>{selectedEvent.attendees.join(", ")}</span>
                    </div>
                  )}

                  {selectedEvent.rating && (
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span>{selectedEvent.rating}/5 stars</span>
                    </div>
                  )}
                </div>

                {selectedEvent.notes && (
                  <div>
                    <h4 className="font-medium text-sm mb-1">Notes</h4>
                    <p className="text-sm text-muted-foreground">{selectedEvent.notes}</p>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    Edit
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    Reschedule
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Legend */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status Legend</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <div className="w-4 h-4 border-l-4 border-l-blue-500 bg-blue-50"></div>
                <span>Planned</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-4 h-4 border-l-4 border-l-yellow-500 bg-yellow-50"></div>
                <span>In Progress</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-4 h-4 border-l-4 border-l-green-500 bg-green-50"></div>
                <span>Completed</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-4 h-4 border-l-4 border-l-red-500 bg-red-50"></div>
                <span>Cancelled</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
