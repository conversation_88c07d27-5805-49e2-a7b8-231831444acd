export interface Activity {
  id: string
  title: string
  description: string
  category: ActivityCategory
  location?: string
  url?: string
  estimatedCost?: number
  duration?: number // in minutes
  difficulty?: 'easy' | 'medium' | 'hard'
  isOutdoor: boolean
  tags: string[]
  createdAt: Date
  updatedAt: Date
  userId: string
}

export interface PlannedActivity {
  id: string
  activityId: string
  activity?: Activity
  scheduledDate: Date
  actualDate?: Date
  status: ActivityStatus
  notes?: string
  actualCost?: number
  rating?: number // 1-5 stars
  photos: string[] // URLs to photos
  attendees: string[]
  weatherCondition?: string
  reminderSet: boolean
  reminderTime?: Date
  isRecurring: boolean
  recurringPattern?: RecurringPattern
  createdAt: Date
  updatedAt: Date
  userId: string
}

export interface ActivityCategory {
  id: string
  name: string
  icon: string
  color: string
  description?: string
}

export interface FileAttachment {
  id: string
  activityId?: string
  plannedActivityId?: string
  fileName: string
  fileType: 'pdf' | 'image' | 'document'
  fileUrl: string
  fileSize: number
  uploadedAt: Date
  userId: string
}

export interface Budget {
  id: string
  name: string
  totalBudget: number
  spentAmount: number
  period: 'monthly' | 'yearly' | 'custom'
  startDate: Date
  endDate?: Date
  categories: string[] // category IDs
  userId: string
}

export interface ActivityHistory {
  id: string
  plannedActivityId: string
  action: 'created' | 'updated' | 'completed' | 'cancelled' | 'rescheduled'
  details: string
  timestamp: Date
  userId: string
}

export interface Wishlist {
  id: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high'
  estimatedCost?: number
  targetDate?: Date
  location?: string
  tags: string[]
  createdAt: Date
  userId: string
}

export interface WeatherData {
  condition: string
  temperature: number
  humidity: number
  windSpeed: number
  description: string
  icon: string
}

export interface Notification {
  id: string
  type: 'reminder' | 'weather_alert' | 'budget_alert' | 'sharing'
  title: string
  message: string
  isRead: boolean
  scheduledFor?: Date
  createdAt: Date
  userId: string
}

export interface SharedActivity {
  id: string
  activityId: string
  sharedBy: string
  sharedWith: string[]
  message?: string
  permissions: 'view' | 'edit'
  expiresAt?: Date
  createdAt: Date
}

export type ActivityStatus = 'planned' | 'in-progress' | 'completed' | 'cancelled'

export type RecurringPattern = {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly'
  interval: number // every X days/weeks/months/years
  daysOfWeek?: number[] // for weekly (0 = Sunday)
  dayOfMonth?: number // for monthly
  endDate?: Date
  occurrences?: number
}

export interface CalendarEvent {
  id: string
  title: string
  start: Date
  end: Date
  allDay?: boolean
  resource?: PlannedActivity
}

export interface ActivityStats {
  totalActivities: number
  completedActivities: number
  totalSpent: number
  averageRating: number
  favoriteCategory: string
  activitiesThisMonth: number
  upcomingActivities: number
}

export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  preferences: UserPreferences
  createdAt: Date
}

export interface UserPreferences {
  defaultView: 'dashboard' | 'calendar' | 'activities'
  notifications: {
    reminders: boolean
    weatherAlerts: boolean
    budgetAlerts: boolean
    sharing: boolean
  }
  currency: string
  timezone: string
  weatherUnit: 'celsius' | 'fahrenheit'
}
