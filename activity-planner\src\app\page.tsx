import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Calendar,
  PlusCircle,
  TrendingUp,
  Clock,
  DollarSign,
  MapPin,
  Star
} from "lucide-react"
import Link from "next/link"

export default function Dashboard() {
  // Mock data - in real app this would come from Supabase
  const stats = {
    totalActivities: 24,
    completedActivities: 18,
    upcomingActivities: 3,
    totalSpent: 1250.50,
    averageRating: 4.2,
    thisMonthActivities: 8
  }

  const upcomingActivities = [
    {
      id: "1",
      title: "Dinner at Italian Restaurant",
      date: "2025-07-10",
      time: "19:00",
      location: "Downtown",
      category: "Food & Dining"
    },
    {
      id: "2",
      title: "Hiking at National Park",
      date: "2025-07-12",
      time: "08:00",
      location: "Mountain Trail",
      category: "Outdoor Adventures"
    },
    {
      id: "3",
      title: "Movie Night",
      date: "2025-07-15",
      time: "20:00",
      location: "Home",
      category: "Entertainment"
    }
  ]

  const recentActivities = [
    {
      id: "1",
      title: "Art Museum Visit",
      date: "2025-07-05",
      rating: 5,
      cost: 25.00
    },
    {
      id: "2",
      title: "Beach Day",
      date: "2025-07-03",
      rating: 4,
      cost: 0
    },
    {
      id: "3",
      title: "Concert",
      date: "2025-07-01",
      rating: 5,
      cost: 85.00
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Welcome back!</h1>
          <p className="text-muted-foreground">Here's what's happening with your activities</p>
        </div>
        <div className="flex gap-2">
          <Link href="/activities/new">
            <Button>
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Activity
            </Button>
          </Link>
          <Link href="/calendar">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              View Calendar
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
            <PlusCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalActivities}</div>
            <p className="text-xs text-muted-foreground">
              +{stats.thisMonthActivities} this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedActivities}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((stats.completedActivities / stats.totalActivities) * 100)}% completion rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalSpent.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Avg ${(stats.totalSpent / stats.completedActivities).toFixed(2)} per activity
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageRating}/5</div>
            <p className="text-xs text-muted-foreground">
              Based on {stats.completedActivities} activities
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Upcoming Activities
            </CardTitle>
            <CardDescription>Your next planned activities</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingActivities.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-1">
                  <h4 className="font-medium">{activity.title}</h4>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {activity.date}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {activity.time}
                    </span>
                    <span className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {activity.location}
                    </span>
                  </div>
                </div>
                <Button variant="outline" size="sm">View</Button>
              </div>
            ))}
            <Link href="/calendar">
              <Button variant="ghost" className="w-full">
                View All Upcoming
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Recent Activities
            </CardTitle>
            <CardDescription>Your recently completed activities</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-1">
                  <h4 className="font-medium">{activity.title}</h4>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{activity.date}</span>
                    <span className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      {activity.rating}/5
                    </span>
                    <span>${activity.cost.toFixed(2)}</span>
                  </div>
                </div>
                <Button variant="outline" size="sm">Review</Button>
              </div>
            ))}
            <Link href="/activities">
              <Button variant="ghost" className="w-full">
                View All Activities
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Get started with common tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link href="/activities/new">
              <Button variant="outline" className="w-full h-20 flex-col gap-2">
                <PlusCircle className="h-6 w-6" />
                Add New Activity
              </Button>
            </Link>
            <Link href="/calendar">
              <Button variant="outline" className="w-full h-20 flex-col gap-2">
                <Calendar className="h-6 w-6" />
                Schedule Activity
              </Button>
            </Link>
            <Link href="/wishlist">
              <Button variant="outline" className="w-full h-20 flex-col gap-2">
                <Star className="h-6 w-6" />
                Add to Wishlist
              </Button>
            </Link>
            <Link href="/analytics">
              <Button variant="outline" className="w-full h-20 flex-col gap-2">
                <TrendingUp className="h-6 w-6" />
                View Analytics
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
