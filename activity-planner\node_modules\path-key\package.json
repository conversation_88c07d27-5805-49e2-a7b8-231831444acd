{"name": "path-key", "version": "3.1.1", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": "sindresorhus/path-key", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}