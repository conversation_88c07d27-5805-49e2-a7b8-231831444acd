{"name": "callsites", "version": "3.1.0", "description": "Get callsites from the V8 stack trace API", "license": "MIT", "repository": "sindresorhus/callsites", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}