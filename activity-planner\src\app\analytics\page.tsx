"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  TrendingUp, 
  TrendingDown,
  Calendar, 
  DollarSign, 
  Star,
  Activity,
  Target,
  Award,
  MapPin,
  Clock,
  Users,
  Download,
  Filter
} from "lucide-react"
import { formatCurrency, getCategoryIcon } from "@/lib/utils"

export default function AnalyticsPage() {
  // Mock data - in real app this would come from Supabase
  const stats = {
    totalActivities: 47,
    completedActivities: 32,
    totalSpent: 2847.50,
    averageRating: 4.3,
    activitiesThisMonth: 8,
    completionRate: 68,
    averageCostPerActivity: 89.00,
    favoriteCategory: "Food & Dining",
    mostActiveMonth: "June 2025",
    totalHoursSpent: 156
  }

  const monthlyData = [
    { month: "Jan", activities: 3, spent: 245.00, rating: 4.2 },
    { month: "Feb", activities: 5, spent: 380.50, rating: 4.1 },
    { month: "Mar", activities: 4, spent: 290.00, rating: 4.4 },
    { month: "Apr", activities: 6, spent: 445.75, rating: 4.3 },
    { month: "May", activities: 7, spent: 520.25, rating: 4.5 },
    { month: "Jun", activities: 9, spent: 680.00, rating: 4.2 },
    { month: "Jul", activities: 8, spent: 286.00, rating: 4.3 }
  ]

  const categoryStats = [
    { category: "Food & Dining", count: 12, spent: 890.50, avgRating: 4.4, icon: "🍽️" },
    { category: "Outdoor Adventures", count: 8, spent: 245.00, avgRating: 4.6, icon: "🌲" },
    { category: "Cultural Experiences", count: 6, spent: 420.00, avgRating: 4.2, icon: "🎭" },
    { category: "Entertainment", count: 4, spent: 180.00, avgRating: 4.1, icon: "🎬" },
    { category: "Date Ideas", count: 2, spent: 312.00, avgRating: 4.8, icon: "💕" }
  ]

  const topActivities = [
    { name: "Hiking at Blue Ridge", completions: 8, avgRating: 4.9, totalSpent: 0 },
    { name: "Movie Nights", completions: 6, avgRating: 4.2, totalSpent: 120.00 },
    { name: "Restaurant Visits", completions: 5, avgRating: 4.5, totalSpent: 650.00 },
    { name: "Art Museum Tours", completions: 4, avgRating: 4.3, totalSpent: 100.00 },
    { name: "Beach Days", completions: 3, avgRating: 4.7, totalSpent: 45.00 }
  ]

  const achievements = [
    { title: "Activity Explorer", description: "Completed 25+ activities", icon: "🏆", unlocked: true },
    { title: "Budget Master", description: "Stayed under budget 5 months in a row", icon: "💰", unlocked: true },
    { title: "Social Butterfly", description: "Completed 10 activities with friends", icon: "🦋", unlocked: true },
    { title: "Adventure Seeker", description: "Tried 5 new outdoor activities", icon: "🎯", unlocked: false },
    { title: "Culture Enthusiast", description: "Visited 10 cultural venues", icon: "🎨", unlocked: false },
    { title: "Foodie", description: "Tried 20 different restaurants", icon: "🍴", unlocked: false }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Analytics</h1>
          <p className="text-muted-foreground">Insights into your activity patterns and preferences</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter Period
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalActivities}</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingUp className="h-3 w-3 text-green-500" />
              +{stats.activitiesThisMonth} this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completionRate}%</div>
            <p className="text-xs text-muted-foreground">
              {stats.completedActivities} of {stats.totalActivities} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalSpent)}</div>
            <p className="text-xs text-muted-foreground">
              Avg {formatCurrency(stats.averageCostPerActivity)} per activity
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageRating}/5</div>
            <p className="text-xs text-muted-foreground">
              Based on {stats.completedActivities} activities
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Activity Trends</CardTitle>
            <CardDescription>Your activity patterns over time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {monthlyData.map((month, index) => (
                <div key={month.month} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-12 text-center font-medium">{month.month}</div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1 text-sm">
                        <Activity className="h-4 w-4" />
                        <span>{month.activities}</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm">
                        <DollarSign className="h-4 w-4" />
                        <span>{formatCurrency(month.spent)}</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span>{month.rating}</span>
                      </div>
                    </div>
                  </div>
                  <div className="w-20 bg-muted rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${(month.activities / 10) * 100}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Category Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Categories</CardTitle>
            <CardDescription>Your preferences by category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {categoryStats.map((category) => (
                <div key={category.category} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{category.icon}</span>
                    <div>
                      <h4 className="font-medium">{category.category}</h4>
                      <div className="flex items-center gap-3 text-sm text-muted-foreground">
                        <span>{category.count} activities</span>
                        <span>{formatCurrency(category.spent)}</span>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span>{category.avgRating}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">{Math.round((category.count / stats.completedActivities) * 100)}%</div>
                    <div className="text-xs text-muted-foreground">of total</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Most Popular Activities</CardTitle>
            <CardDescription>Your most frequently completed activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topActivities.map((activity, index) => (
                <div key={activity.name} className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{activity.name}</h4>
                    <div className="flex items-center gap-3 text-sm text-muted-foreground">
                      <span>{activity.completions} times</span>
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        <span>{activity.avgRating}</span>
                      </div>
                      <span>{formatCurrency(activity.totalSpent)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Achievements */}
        <Card>
          <CardHeader>
            <CardTitle>Achievements</CardTitle>
            <CardDescription>Your activity milestones and badges</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {achievements.map((achievement) => (
                <div 
                  key={achievement.title} 
                  className={`flex items-center gap-3 p-3 border rounded-lg ${
                    achievement.unlocked ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200 opacity-60'
                  }`}
                >
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-medium flex items-center gap-2">
                      {achievement.title}
                      {achievement.unlocked && <Award className="h-4 w-4 text-green-500" />}
                    </h4>
                    <p className="text-sm text-muted-foreground">{achievement.description}</p>
                  </div>
                  {achievement.unlocked && (
                    <div className="text-green-500 font-medium text-sm">Unlocked!</div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Time Investment</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-primary">{stats.totalHoursSpent}</div>
              <p className="text-sm text-muted-foreground">Total hours spent on activities</p>
              <p className="text-xs text-muted-foreground">
                Avg {Math.round(stats.totalHoursSpent / stats.completedActivities)} hours per activity
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Favorite Category</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-3xl">{getCategoryIcon(stats.favoriteCategory)}</div>
              <div className="font-semibold">{stats.favoriteCategory}</div>
              <p className="text-sm text-muted-foreground">
                {categoryStats.find(c => c.category === stats.favoriteCategory)?.count} activities
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Most Active Period</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-2xl font-bold text-primary">{stats.mostActiveMonth}</div>
              <p className="text-sm text-muted-foreground">Your most active month</p>
              <p className="text-xs text-muted-foreground">
                {monthlyData.find(m => `${m.month} 2025` === stats.mostActiveMonth)?.activities} activities completed
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
