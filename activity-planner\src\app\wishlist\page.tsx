"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Search,
  Filter,
  PlusCircle,
  MapPin,
  DollarSign,
  Calendar,
  Star,
  ArrowUp,
  ArrowDown,
  Minus,
  Heart,
  Edit,
  Trash2,
  CheckCircle
} from "lucide-react"
import { Wishlist } from "@/types"
import { formatCurrency, formatDate } from "@/lib/utils"

export default function WishlistPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedPriority, setSelectedPriority] = useState("all")
  const [sortBy, setSortBy] = useState("priority")

  // Mock data - in real app this would come from Supabase
  const wishlistItems: Wishlist[] = [
    {
      id: "1",
      title: "Trip to Japan",
      description: "Two-week cultural immersion trip including Tokyo, Kyoto, and Osaka. Visit temples, try authentic cuisine, and experience traditional culture.",
      priority: "high",
      estimatedCost: 4500.00,
      targetDate: new Date("2025-12-01"),
      location: "Japan",
      tags: ["travel", "culture", "international", "bucket-list"],
      createdAt: new Date("2025-06-15"),
      userId: "user1"
    },
    {
      id: "2",
      title: "Learn Scuba Diving",
      description: "Get PADI certified and explore underwater world. Start with pool training then open water certification.",
      priority: "medium",
      estimatedCost: 650.00,
      targetDate: new Date("2025-09-15"),
      location: "Local Dive Center",
      tags: ["adventure", "certification", "underwater", "skills"],
      createdAt: new Date("2025-06-20"),
      userId: "user1"
    },
    {
      id: "3",
      title: "Hot Air Balloon Ride",
      description: "Romantic sunrise hot air balloon ride over wine country with champagne breakfast.",
      priority: "medium",
      estimatedCost: 350.00,
      targetDate: new Date("2025-08-20"),
      location: "Napa Valley",
      tags: ["romantic", "adventure", "scenic", "special-occasion"],
      createdAt: new Date("2025-07-01"),
      userId: "user1"
    },
    {
      id: "4",
      title: "Cooking Class with Celebrity Chef",
      description: "Exclusive hands-on cooking class with Michelin-starred chef. Learn advanced techniques and create 5-course meal.",
      priority: "low",
      estimatedCost: 450.00,
      location: "Culinary Institute",
      tags: ["cooking", "luxury", "learning", "food"],
      createdAt: new Date("2025-06-25"),
      userId: "user1"
    },
    {
      id: "5",
      title: "Northern Lights Photography Tour",
      description: "Week-long photography expedition to Iceland to capture the Aurora Borealis with professional guidance.",
      priority: "high",
      estimatedCost: 3200.00,
      targetDate: new Date("2026-02-15"),
      location: "Iceland",
      tags: ["photography", "travel", "nature", "winter", "bucket-list"],
      createdAt: new Date("2025-07-03"),
      userId: "user1"
    },
    {
      id: "6",
      title: "Wine Tasting in Tuscany",
      description: "Long weekend in Tuscany visiting family-owned vineyards, learning about wine making, and enjoying Italian countryside.",
      priority: "medium",
      estimatedCost: 1800.00,
      targetDate: new Date("2025-10-10"),
      location: "Tuscany, Italy",
      tags: ["wine", "travel", "culture", "relaxation"],
      createdAt: new Date("2025-06-30"),
      userId: "user1"
    }
  ]

  const priorities = [
    { id: "all", name: "All Priorities", count: wishlistItems.length },
    { id: "high", name: "High Priority", count: wishlistItems.filter(w => w.priority === "high").length },
    { id: "medium", name: "Medium Priority", count: wishlistItems.filter(w => w.priority === "medium").length },
    { id: "low", name: "Low Priority", count: wishlistItems.filter(w => w.priority === "low").length },
  ]

  const filteredItems = wishlistItems
    .filter(item => {
      const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesPriority = selectedPriority === "all" || item.priority === selectedPriority
      
      return matchesSearch && matchesPriority
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "priority":
          const priorityOrder = { high: 3, medium: 2, low: 1 }
          return priorityOrder[b.priority] - priorityOrder[a.priority]
        case "cost":
          return (b.estimatedCost || 0) - (a.estimatedCost || 0)
        case "date":
          if (!a.targetDate && !b.targetDate) return 0
          if (!a.targetDate) return 1
          if (!b.targetDate) return -1
          return a.targetDate.getTime() - b.targetDate.getTime()
        case "created":
          return b.createdAt.getTime() - a.createdAt.getTime()
        default:
          return 0
      }
    })

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "high": return <ArrowUp className="h-4 w-4 text-red-500" />
      case "medium": return <Minus className="h-4 w-4 text-yellow-500" />
      case "low": return <ArrowDown className="h-4 w-4 text-green-500" />
      default: return null
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800 border-red-200"
      case "medium": return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "low": return "bg-green-100 text-green-800 border-green-200"
      default: return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const totalEstimatedCost = filteredItems.reduce((sum, item) => sum + (item.estimatedCost || 0), 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Wishlist</h1>
          <p className="text-muted-foreground">Dream activities and future adventures</p>
        </div>
        <Button>
          <PlusCircle className="h-4 w-4 mr-2" />
          Add to Wishlist
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm text-muted-foreground">Total Items</p>
                <p className="text-2xl font-bold">{filteredItems.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <ArrowUp className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm text-muted-foreground">High Priority</p>
                <p className="text-2xl font-bold">{wishlistItems.filter(w => w.priority === "high").length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Total Cost</p>
                <p className="text-2xl font-bold">{formatCurrency(totalEstimatedCost)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">This Year</p>
                <p className="text-2xl font-bold">
                  {wishlistItems.filter(w => w.targetDate && w.targetDate.getFullYear() === 2025).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search wishlist items..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 border border-input rounded-md bg-background"
          >
            <option value="priority">Sort by Priority</option>
            <option value="cost">Sort by Cost</option>
            <option value="date">Sort by Target Date</option>
            <option value="created">Sort by Created</option>
          </select>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            More Filters
          </Button>
        </div>
      </div>

      {/* Priority Filters */}
      <div className="flex flex-wrap gap-2">
        {priorities.map((priority) => (
          <Button
            key={priority.id}
            variant={selectedPriority === priority.id ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedPriority(priority.id)}
            className="flex items-center gap-2"
          >
            {priority.id !== "all" && getPriorityIcon(priority.id)}
            <span>{priority.name}</span>
            <span className="bg-muted text-muted-foreground px-1.5 py-0.5 rounded-full text-xs">
              {priority.count}
            </span>
          </Button>
        ))}
      </div>

      {/* Wishlist Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredItems.map((item) => (
          <Card key={item.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(item.priority)}`}>
                      {getPriorityIcon(item.priority)}
                      <span className="ml-1 capitalize">{item.priority}</span>
                    </span>
                  </div>
                  <CardTitle className="text-lg">{item.title}</CardTitle>
                  <CardDescription className="line-clamp-3 mt-2">
                    {item.description}
                  </CardDescription>
                </div>
                <div className="flex gap-1">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Details */}
              <div className="space-y-2">
                {item.location && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span>{item.location}</span>
                  </div>
                )}
                
                {item.estimatedCost && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <DollarSign className="h-4 w-4" />
                    <span>{formatCurrency(item.estimatedCost)}</span>
                  </div>
                )}

                {item.targetDate && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>Target: {formatDate(item.targetDate)}</span>
                  </div>
                )}
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-1">
                {item.tags.slice(0, 3).map((tag) => (
                  <span key={tag} className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {tag}
                  </span>
                ))}
                {item.tags.length > 3 && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    +{item.tags.length - 3} more
                  </span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button size="sm" className="flex-1">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Plan This
                </Button>
                <Button variant="outline" size="sm">
                  <Star className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredItems.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="space-y-4">
              <div className="text-6xl">✨</div>
              <div>
                <h3 className="text-lg font-semibold">No wishlist items found</h3>
                <p className="text-muted-foreground">
                  {searchTerm || selectedPriority !== "all" 
                    ? "Try adjusting your search or filters"
                    : "Start building your dream activity list"
                  }
                </p>
              </div>
              <Button>
                <PlusCircle className="h-4 w-4 mr-2" />
                Add Your First Dream Activity
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
