Set WshShell = CreateObject("WScript.Shell")
Set oShellLink = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\Activity Planner.lnk")

' Get the current directory
currentDir = CreateObject("Scripting.FileSystemObject").GetAbsolutePathName(".")

' Set shortcut properties
oShellLink.TargetPath = "powershell.exe"
oShellLink.Arguments = "-ExecutionPolicy Bypass -File """ & currentDir & "\start-activity-planner.ps1"""
oShellLink.WorkingDirectory = currentDir
oShellLink.Description = "Activity Planner - Personal Activity Management App"
oShellLink.WindowStyle = 1

' Try to set an icon (using PowerShell icon)
oShellLink.IconLocation = "powershell.exe,0"

' Save the shortcut
oShellLink.Save

WScript.Echo "Desktop shortcut created successfully!" & vbCrLf & vbCrLf & "You can now double-click 'Activity Planner' on your desktop to start the app."
