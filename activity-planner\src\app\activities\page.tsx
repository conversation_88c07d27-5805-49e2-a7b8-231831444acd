"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Search, 
  Filter, 
  PlusCircle, 
  MapPin, 
  DollarSign, 
  Clock,
  Star,
  Calendar,
  ExternalLink,
  Edit,
  Trash2
} from "lucide-react"
import Link from "next/link"
import { getCategoryIcon, formatCurrency } from "@/lib/utils"

export default function ActivitiesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")

  // Mock data - in real app this would come from Supabase
  const activities = [
    {
      id: "1",
      title: "Romantic Dinner at Chez Laurent",
      description: "Fine French dining experience with wine pairing",
      category: "date",
      location: "Downtown French Quarter",
      url: "https://chezlaurent.com",
      estimatedCost: 150.00,
      duration: 120,
      difficulty: "easy",
      isOutdoor: false,
      tags: ["romantic", "fine-dining", "wine"],
      createdAt: new Date("2025-07-01"),
      timesCompleted: 2,
      averageRating: 4.5
    },
    {
      id: "2",
      title: "Hiking at Blue Ridge Trail",
      description: "Scenic mountain trail with waterfall views",
      category: "outdoor",
      location: "Blue Ridge National Park",
      estimatedCost: 0,
      duration: 240,
      difficulty: "medium",
      isOutdoor: true,
      tags: ["hiking", "nature", "exercise"],
      createdAt: new Date("2025-06-15"),
      timesCompleted: 5,
      averageRating: 5.0
    },
    {
      id: "3",
      title: "Cooking Class - Italian Cuisine",
      description: "Learn to make authentic pasta and risotto",
      category: "learning",
      location: "Culinary Institute",
      url: "https://culinaryinstitute.com/classes",
      estimatedCost: 85.00,
      duration: 180,
      difficulty: "medium",
      isOutdoor: false,
      tags: ["cooking", "italian", "hands-on"],
      createdAt: new Date("2025-06-20"),
      timesCompleted: 1,
      averageRating: 4.8
    },
    {
      id: "4",
      title: "Art Museum Exhibition",
      description: "Modern art exhibition featuring local artists",
      category: "cultural",
      location: "Metropolitan Art Museum",
      url: "https://metmuseum.org",
      estimatedCost: 25.00,
      duration: 90,
      difficulty: "easy",
      isOutdoor: false,
      tags: ["art", "culture", "educational"],
      createdAt: new Date("2025-07-03"),
      timesCompleted: 0,
      averageRating: 0
    },
    {
      id: "5",
      title: "Beach Volleyball Tournament",
      description: "Join local beach volleyball league",
      category: "sports",
      location: "Sunset Beach",
      estimatedCost: 20.00,
      duration: 180,
      difficulty: "hard",
      isOutdoor: true,
      tags: ["volleyball", "beach", "competitive"],
      createdAt: new Date("2025-06-25"),
      timesCompleted: 3,
      averageRating: 4.2
    }
  ]

  const categories = [
    { id: "all", name: "All Categories", count: activities.length },
    { id: "date", name: "Date Ideas", count: activities.filter(a => a.category === "date").length },
    { id: "outdoor", name: "Outdoor", count: activities.filter(a => a.category === "outdoor").length },
    { id: "indoor", name: "Indoor", count: activities.filter(a => a.category === "indoor").length },
    { id: "cultural", name: "Cultural", count: activities.filter(a => a.category === "cultural").length },
    { id: "food", name: "Food & Dining", count: activities.filter(a => a.category === "food").length },
    { id: "sports", name: "Sports", count: activities.filter(a => a.category === "sports").length },
    { id: "learning", name: "Learning", count: activities.filter(a => a.category === "learning").length },
  ]

  const filteredActivities = activities.filter(activity => {
    const matchesSearch = activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === "all" || activity.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy": return "bg-green-100 text-green-800"
      case "medium": return "bg-yellow-100 text-yellow-800"
      case "hard": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Activities</h1>
          <p className="text-muted-foreground">Manage your activity ideas and experiences</p>
        </div>
        <Link href="/activities/new">
          <Button>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add New Activity
          </Button>
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search activities, descriptions, or tags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          More Filters
        </Button>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
            className="flex items-center gap-2"
          >
            <span>{getCategoryIcon(category.name)}</span>
            <span>{category.name}</span>
            <span className="bg-muted text-muted-foreground px-1.5 py-0.5 rounded-full text-xs">
              {category.count}
            </span>
          </Button>
        ))}
      </div>

      {/* Activities Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredActivities.map((activity) => (
          <Card key={activity.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{getCategoryIcon(activity.category)}</span>
                  <div>
                    <CardTitle className="text-lg">{activity.title}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {activity.description}
                    </CardDescription>
                  </div>
                </div>
                <div className="flex gap-1">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Activity Details */}
              <div className="space-y-2">
                {activity.location && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span>{activity.location}</span>
                  </div>
                )}
                
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4" />
                    <span>{formatCurrency(activity.estimatedCost)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{activity.duration}min</span>
                  </div>
                </div>

                {activity.averageRating > 0 && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span>{activity.averageRating.toFixed(1)}</span>
                    </div>
                    <span className="text-muted-foreground">
                      ({activity.timesCompleted} completed)
                    </span>
                  </div>
                )}
              </div>

              {/* Tags and Difficulty */}
              <div className="flex flex-wrap gap-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(activity.difficulty)}`}>
                  {activity.difficulty}
                </span>
                {activity.isOutdoor && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Outdoor
                  </span>
                )}
                {activity.tags.slice(0, 2).map((tag) => (
                  <span key={tag} className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {tag}
                  </span>
                ))}
                {activity.tags.length > 2 && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    +{activity.tags.length - 2} more
                  </span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Link href={`/calendar?schedule=${activity.id}`} className="flex-1">
                  <Button variant="outline" size="sm" className="w-full">
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule
                  </Button>
                </Link>
                {activity.url && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={activity.url} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredActivities.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="space-y-4">
              <div className="text-6xl">🎯</div>
              <div>
                <h3 className="text-lg font-semibold">No activities found</h3>
                <p className="text-muted-foreground">
                  {searchTerm || selectedCategory !== "all" 
                    ? "Try adjusting your search or filters"
                    : "Get started by adding your first activity"
                  }
                </p>
              </div>
              <Link href="/activities/new">
                <Button>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Add Your First Activity
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
